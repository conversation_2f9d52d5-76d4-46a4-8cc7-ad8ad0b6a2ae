# Vault Configuration Module

This Terraform module provides comprehensive configuration for HashiCorp Vault, following security best practices and supporting multiple authentication methods, secrets engines, and policies.

## Features

- **Multiple Authentication Methods**: Kubernetes, UserPass, AppRole
- **Secure Provider Configuration**: Uses auth_login blocks instead of static tokens
- **Default Security Policies**: Pre-configured policies for common use cases
- **KV Secrets Engines**: Support for both v1 and v2 KV engines
- **Database Secrets**: Database secrets engine configuration
- **Audit Logging**: Configurable audit devices
- **Kubernetes Integration**: Service account and RBAC configuration for cluster access
- **Enterprise Features**: Namespace support for Vault Enterprise

## Best Practices Implemented

- Uses environment variables and auth_login for provider authentication
- Follows principle of least privilege for policies
- Implements proper Kubernetes RBAC for service accounts
- Configures secure token TTLs and rotation
- Separates concerns with modular configuration
- Provides comprehensive outputs for integration

## Usage

### Basic Configuration with Kubernetes Authentication

```hcl
module "vault_config" {
  source = "../modules/vault_config"

  vault_address = "http://vault.vault.svc.cluster.local:8200"
  
  # Kubernetes authentication configuration
  kubernetes_auth_config = {
    enabled                = true
    path                   = "kubernetes"
    kubernetes_host        = var.cluster_endpoint
    kubernetes_ca_cert     = var.cluster_ca_certificate
    disable_iss_validation = true
  }

  # Define Kubernetes auth roles for applications
  kubernetes_auth_roles = {
    "app-role" = {
      bound_service_account_names      = ["myapp"]
      bound_service_account_namespaces = ["default"]
      token_policies                   = ["app-read-policy"]
      token_ttl                        = 3600
      token_max_ttl                    = 86400
    }
    "admin-role" = {
      bound_service_account_names      = ["vault-admin"]
      bound_service_account_namespaces = ["vault"]
      token_policies                   = ["admin-policy"]
      token_ttl                        = 7200
      token_max_ttl                    = 43200
    }
  }

  # Configure KV secrets engines
  kv_secrets_engines = {
    "app-secrets" = {
      path        = "secret/app"
      description = "Application secrets"
      version     = 2
      max_versions = 5
      secrets = {
        "database" = {
          username = "myapp"
          password = "secure-password"
        }
        "api-keys" = {
          third_party_api = "api-key-value"
        }
      }
    }
  }

  # Custom policies
  vault_policies = {
    "myapp-policy" = {
      policy = <<-EOT
        path "secret/data/app/myapp/*" {
          capabilities = ["read"]
        }
      EOT
    }
  }

  # Enable audit logging
  audit_devices = {
    "file" = {
      type = "file"
      options = {
        file_path = "/vault/audit/audit.log"
      }
    }
  }

  # Cluster connection details
  cluster_endpoint       = var.cluster_endpoint
  cluster_ca_certificate = var.cluster_ca_certificate
  client_token          = var.client_token
}
```

### Advanced Configuration with Multiple Auth Methods

```hcl
module "vault_config" {
  source = "../modules/vault_config"

  vault_address = "https://vault.company.com:8200"
  
  # Enable multiple auth methods
  kubernetes_auth_config = {
    enabled                = true
    kubernetes_host        = var.cluster_endpoint
    kubernetes_ca_cert     = var.cluster_ca_certificate
  }

  userpass_auth = {
    enabled = true
    users = {
      "admin" = {
        password = var.admin_password
        policies = ["admin-policy"]
      }
      "developer" = {
        password = var.dev_password
        policies = ["app-read-policy"]
      }
    }
  }

  approle_auth = {
    enabled = true
    roles = {
      "cicd" = {
        token_policies = ["cicd-policy"]
        token_ttl      = 1800
        token_max_ttl  = 3600
      }
    }
  }

  # Database secrets engine
  database_secrets_engines = {
    "postgres" = {
      path        = "database"
      description = "PostgreSQL dynamic credentials"
    }
  }
}
```

## Authentication Methods

### Kubernetes Authentication

Recommended for applications running in Kubernetes clusters:

- Automatically configures service accounts and RBAC
- Uses JWT tokens for secure authentication
- Supports audience validation and issuer checking

### UserPass Authentication

For human users and development environments:

- Username/password authentication
- Suitable for interactive access
- Can be integrated with external identity providers

### AppRole Authentication

For CI/CD systems and applications:

- Machine-to-machine authentication
- Role-based access with secret IDs
- Supports token rotation and usage limits

## Default Policies

The module includes three default policies:

1. **app-read-policy**: Basic read access for applications
2. **admin-policy**: Administrative access with safety restrictions
3. **cicd-policy**: CI/CD pipeline access for deployments

## Secrets Engines

### KV Secrets Engine

- Supports both v1 and v2 KV engines
- Configurable versioning and retention
- Secret seeding capability

### Database Secrets Engine

- Dynamic database credential generation
- Configurable TTLs and rotation
- Support for multiple database types

## Security Considerations

1. **Token Management**: All tokens have limited TTLs and can be revoked
2. **Network Security**: Assumes Vault is accessible only from within the cluster
3. **Audit Logging**: Enable audit devices for compliance and monitoring
4. **Least Privilege**: Default policies follow minimal access principles
5. **Secret Rotation**: Supports automatic credential rotation

## Variables

| Name | Description | Type | Default | Required |
|------|-------------|------|---------|:--------:|
| vault_address | Vault cluster address | `string` | n/a | yes |
| kubernetes_auth_config | Kubernetes auth configuration | `object` | `null` | no |
| kubernetes_auth_roles | Kubernetes auth roles | `map(object)` | `{}` | no |
| vault_policies | Custom Vault policies | `map(object)` | `{}` | no |
| kv_secrets_engines | KV secrets engines configuration | `map(object)` | `{}` | no |
| audit_devices | Audit devices configuration | `map(object)` | `{}` | no |

## Outputs

| Name | Description |
|------|-------------|
| kubernetes_auth_path | Kubernetes auth mount path |
| vault_policies | Created policies information |
| kv_secrets_engines | KV engines information |
| vault_configuration_summary | Configuration summary |

## Dependencies

- HashiCorp Vault cluster (accessible from Terraform execution environment)
- Kubernetes cluster (for Kubernetes authentication)
- Appropriate network connectivity and firewall rules

## Terraform Version

- Terraform >= 1.0
- Vault provider >= 4.0
- Kubernetes provider >= 2.10.0

## Production Considerations

1. **TLS Configuration**: Disable `skip_tls_verify` in production
2. **High Availability**: Configure Vault HA with proper storage backend
3. **Backup Strategy**: Implement regular backup procedures
4. **Monitoring**: Set up Vault metrics and alerting
5. **Disaster Recovery**: Plan for Vault recovery procedures

## Troubleshooting

### Common Issues

1. **Authentication Failures**: Check service account tokens and RBAC
2. **Network Connectivity**: Verify Vault address and network policies
3. **Policy Errors**: Validate HCL syntax in policy documents
4. **Token Expiration**: Monitor token TTLs and renewal

### Debug Commands

```bash
# Check Vault status
vault status

# List auth methods
vault auth list

# Check policy
vault policy read policy-name

# Test authentication
vault write auth/kubernetes/login role=role-name jwt=@jwt-token
```
