variable "github_org_name" {
  description = "The name of the GitHub organization."
  type        = string
}

variable "repositories" {
  description = "Map of repositories to manage. Key is logical name, value contains name and default_branch."
  type = map(object({
    name                 = string
    default_branch       = string
    has_wiki             = optional(bool, true)  # Example optional attribute
    archived             = optional(bool, false) # Allow archiving repositories via input
    vulnerability_alerts = optional(bool, true)  # Enable vulnerability alerts by default
    pages = optional(object({
      build_type = optional(string, "legacy")
      source = object({
        branch = string
        path   = optional(string, "/")
      })
    }))
    # Add other repo settings as needed
  }))
  default = {}
}

variable "teams" {
  description = "Map of teams to manage. Key is team slug, value contains name, privacy, and parent_slug (null for base teams)."
  type = map(object({
    name        = string
    privacy     = string
    parent_slug = optional(string) # Use optional(string) which defaults to null
  }))
  default = {}
}

variable "members" {
  description = "Map of organization members and their roles (admin or member)."
  type        = map(string)
  default     = {}
}

variable "team_memberships" {
  description = "Map defining team memberships. Key is team slug, value is a map of username -> role (member or maintainer)."
  type        = map(map(string))
  default     = {}
}

variable "team_repository_permissions" {
  description = "Map defining team repository permissions. Key is team slug, value is a map of repository logical name -> permission (pull, push, maintain, admin)."
  type        = map(map(string))
  default     = {}
}

variable "branch_protections" {
  description = "Defines specific branch protection rules. Key is an arbitrary unique identifier (e.g., 'repo-main-protection', 'repo-dev-protection'). Value specifies the repo, pattern, and rules."
  type = map(object({
    repository_key = string # Logical key matching a key in var.repositories
    pattern        = string # Branch name or pattern (e.g., "main", "develop", "release/*")

    # Common Protection Rules (add more arguments from github_branch_protection as needed)
    enforce_admins                  = optional(bool, false)
    require_signed_commits          = optional(bool, false)
    require_conversation_resolution = optional(bool, true)
    allows_deletions                = optional(bool, false)

    required_approving_review_count = optional(number, 1)   # From required_pull_request_reviews block
    dismiss_stale_reviews           = optional(bool, true)  # From required_pull_request_reviews block
    require_code_owner_reviews      = optional(bool, false) # From required_pull_request_reviews block

    # NEW: Restrict who can dismiss reviews
    restrict_dismissals    = optional(bool, false)      # From required_pull_request_reviews block
    dismissal_restrictions = optional(list(string), []) # List of team/user slugs allowed to dismiss

    # For required_status_checks block
    require_status_checks = optional(bool, true) # Simplified flag to enable/disable the block
    strict_status_checks  = optional(bool, true) # require_branches_to_be_up_to_date
    status_check_contexts = optional(list(string), [])

    # Pull request bypassers
    pull_request_bypassers = optional(list(string), []) # List of actors allowed to bypass

    # Restrict who can push to a branch
    restrict_pushes  = optional(bool, false)
    blocks_creations = optional(bool, false) # If true, new branches matching pattern cannot be created by non-allowed users.

    # List of actors (user/team slugs, e.g., "my_user", "my_org/my_team_slug")
    # allowed to push directly to the branch. This is used if restrict_pushes is true.
    push_allowances = optional(list(string), [])
  }))
  default = {}
}

variable "organization_settings" {
  description = "Settings for the GitHub organization. See https://registry.terraform.io/providers/integrations/github/latest/docs/resources/organization_settings"
  type = object({
    billing_email                                                = string
    blog                                                         = optional(string)
    company                                                      = optional(string)
    email                                                        = optional(string)
    twitter_username                                             = optional(string)
    location                                                     = optional(string)
    name                                                         = optional(string)
    description                                                  = optional(string)
    has_organization_projects                                    = optional(bool, true)
    has_repository_projects                                      = optional(bool, true)
    default_repository_permission                                = optional(string, "read")
    members_can_create_repositories                              = optional(bool, false)
    members_can_create_public_repositories                       = optional(bool, false)
    members_can_create_private_repositories                      = optional(bool, false)
    members_can_create_internal_repositories                     = optional(bool, false)
    members_can_create_pages                                     = optional(bool, false)
    members_can_create_public_pages                              = optional(bool, false)
    members_can_create_private_pages                             = optional(bool, false)
    members_can_fork_private_repositories                        = optional(bool, false)
    web_commit_signoff_required                                  = optional(bool, true)
    advanced_security_enabled_for_new_repositories               = optional(bool, true)
    dependabot_alerts_enabled_for_new_repositories               = optional(bool, true)
    dependabot_security_updates_enabled_for_new_repositories     = optional(bool, true)
    dependency_graph_enabled_for_new_repositories                = optional(bool, true)
    secret_scanning_enabled_for_new_repositories                 = optional(bool, true)
    secret_scanning_push_protection_enabled_for_new_repositories = optional(bool, true)
  })
  default = {
    billing_email                                                = "<EMAIL>"
    has_organization_projects                                    = true
    has_repository_projects                                      = true
    default_repository_permission                                = "read"
    members_can_create_repositories                              = false
    members_can_create_public_repositories                       = false
    members_can_create_private_repositories                      = false
    members_can_create_internal_repositories                     = false
    members_can_create_pages                                     = false
    members_can_create_public_pages                              = false
    members_can_create_private_pages                             = false
    members_can_fork_private_repositories                        = false
    web_commit_signoff_required                                  = true
    advanced_security_enabled_for_new_repositories               = true
    dependabot_alerts_enabled_for_new_repositories               = true
    dependabot_security_updates_enabled_for_new_repositories     = true
    dependency_graph_enabled_for_new_repositories                = true
    secret_scanning_enabled_for_new_repositories                 = true
    secret_scanning_push_protection_enabled_for_new_repositories = true
  }
}