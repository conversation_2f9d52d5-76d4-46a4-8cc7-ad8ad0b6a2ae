# Use the provided org name for data sources or provider config if needed
# (Provider config is usually handled by <PERSON>grun<PERSON>)

locals {
  # Reconstruct the necessary locals using input variables
  _all_teams = var.teams

  # Flatten the input maps for for_each loops
  flat_team_memberships = flatten([
    for team_slug, members in var.team_memberships : [
      for username, role in members : {
        team_slug = team_slug
        username  = username
        role      = role
      }
    ]
  ])

  flat_team_repo_permissions = flatten([
    for team_slug, repos in var.team_repository_permissions : [
      for repo_name, permission in repos : {
        team_slug  = team_slug
        repo_name  = repo_name
        permission = permission
      }
    ]
  ])

  # --- Refined Team Level Calculation ---
  # Level 0: No parent
  level0_teams = { for slug, team in var.teams : slug => team if team.parent_slug == null }
  level0_slugs = keys(local.level0_teams)

  # Filter teams with parents first, then check parent level
  teams_with_parents = { for slug, team in var.teams : slug => team if team.parent_slug != null }

  # Level 1: Parent is Level 0
  level1_teams = { for slug, team in local.teams_with_parents : slug => team if contains(local.level0_slugs, team.parent_slug) }
  level1_slugs = keys(local.level1_teams)

  # Level 2: Parent is Level 1
  level2_teams = { for slug, team in local.teams_with_parents : slug => team if contains(local.level1_slugs, team.parent_slug) }
  level2_slugs = keys(local.level2_teams)

  # Level 3: Parent is Level 2
  level3_teams = { for slug, team in local.teams_with_parents : slug => team if contains(local.level2_slugs, team.parent_slug) }
  level3_slugs = keys(local.level3_teams)
  # --- End Refined Calculation ---

  # All teams merged for lookups (using the actual resource outputs)
  all_teams = merge(
    github_team.level0,
    github_team.level1,
    github_team.level2,
    github_team.level3
  )

  # Constants for organization settings (these can be overridden by inputs)
  organization_billing_email = var.organization_settings.billing_email
  organization_company       = lookup(var.organization_settings, "company", null)
  organization_blog          = lookup(var.organization_settings, "blog", null)
  organization_email         = lookup(var.organization_settings, "email", null)
  organization_twitter       = lookup(var.organization_settings, "twitter_username", null)
  organization_location      = lookup(var.organization_settings, "location", null)
  organization_name          = lookup(var.organization_settings, "name", null)
  organization_description   = lookup(var.organization_settings, "description", null)
}

# ==================== Repositories ====================
resource "github_repository" "main" {
  for_each = var.repositories

  name                        = each.value.name
  visibility                  = "private"
  has_issues                  = true
  has_projects                = true
  has_wiki                    = lookup(each.value, "has_wiki", true)
  has_downloads               = true
  is_template                 = false
  archived                    = lookup(each.value, "archived", false)
  allow_auto_merge            = false
  allow_merge_commit          = false
  allow_rebase_merge          = true
  allow_squash_merge          = true
  delete_branch_on_merge      = true
  squash_merge_commit_title   = "COMMIT_OR_PR_TITLE"
  squash_merge_commit_message = "COMMIT_MESSAGES"
  merge_commit_title          = "MERGE_MESSAGE"
  merge_commit_message        = "PR_TITLE"
  vulnerability_alerts        = lookup(each.value, "vulnerability_alerts", true)

  dynamic "pages" {
    for_each = lookup(each.value, "pages", null) != null ? [lookup(each.value, "pages")] : []
    content {
      build_type = lookup(pages.value, "build_type", "legacy")
      source {
        branch = pages.value.source.branch
        path   = lookup(pages.value.source, "path", "/")
      }
    }
  }
}

# ==================== Branches & Protection ====================
resource "github_branch" "default" {
  for_each   = var.repositories
  repository = github_repository.main[each.key].name
  branch     = each.value.default_branch
}

resource "github_branch_default" "main" {
  for_each   = var.repositories
  repository = github_repository.main[each.key].name
  branch     = github_branch.default[each.key].branch
}

# ==================== Members ====================
resource "github_membership" "main" {
  for_each = var.members
  username = each.key
  role     = each.value
}

# ===================== Teams (By Level) ======================
# Create teams level by level, ensuring dependencies are met
resource "github_team" "level0" {
  for_each = local.level0_teams
  name     = each.value.name
  privacy  = each.value.privacy
}

resource "github_team" "level1" {
  for_each       = local.level1_teams
  name           = each.value.name
  privacy        = each.value.privacy
  parent_team_id = github_team.level0[each.value.parent_slug].id
}

resource "github_team" "level2" {
  for_each       = local.level2_teams
  name           = each.value.name
  privacy        = each.value.privacy
  parent_team_id = github_team.level1[each.value.parent_slug].id
}

resource "github_team" "level3" {
  for_each       = local.level3_teams
  name           = each.value.name
  privacy        = each.value.privacy
  parent_team_id = github_team.level2[each.value.parent_slug].id
}

# ===================== Team Memberships ======================
resource "github_team_membership" "main" {
  # Use a map comprehension for a stable for_each key
  for_each = { for mem in local.flat_team_memberships : "${mem.team_slug}-${mem.username}" => mem }

  team_id  = local.all_teams[each.value.team_slug].id
  username = each.value.username
  role     = each.value.role
}

# ===================== Team Repositories ======================
resource "github_team_repository" "main" {
  # Use a map comprehension for a stable for_each key
  for_each = { for perm in local.flat_team_repo_permissions : "${perm.team_slug}-${perm.repo_name}" => perm }

  team_id = local.all_teams[each.value.team_slug].id
  # Use lookup with null default and check in precondition for safety
  repository = lookup(var.repositories, each.value.repo_name, null) != null ? github_repository.main[each.value.repo_name].name : null
  permission = each.value.permission

  # Add lifecycle rule to prevent errors if repository key is invalid
  lifecycle {
    precondition {
      condition     = contains(keys(var.repositories), each.value.repo_name)
      error_message = "Repository key \"${each.value.repo_name}\" not found in var.repositories for team permission \"${each.key}\"."
    }
    # Ensure the team exists before trying to assign repo permissions
    precondition {
      condition     = contains(keys(local.all_teams), each.value.team_slug)
      error_message = "Team slug \"${each.value.team_slug}\" not found or not created, cannot assign repository permissions."
    }
  }
}

# ===================== Branch Protection ======================
resource "github_branch_protection" "custom" {
  for_each = var.branch_protections

  repository_id = contains(keys(var.repositories), each.value.repository_key) ? github_repository.main[each.value.repository_key].node_id : tostring(null)
  pattern       = each.value.pattern

  enforce_admins         = lookup(each.value, "enforce_admins", false)
  allows_deletions       = lookup(each.value, "allows_deletions", false)
  require_signed_commits = lookup(each.value, "require_signed_commits", true)

  required_pull_request_reviews {
    required_approving_review_count = lookup(each.value, "required_approving_review_count", 1)
    dismiss_stale_reviews           = lookup(each.value, "dismiss_stale_reviews", false)
    require_code_owner_reviews      = lookup(each.value, "require_code_owner_reviews", false)
    pull_request_bypassers          = lookup(each.value, "pull_request_bypassers", [])
    restrict_dismissals             = lookup(each.value, "restrict_dismissals", false)
    dismissal_restrictions          = lookup(each.value, "dismissal_restrictions", [])
  }

  dynamic "required_status_checks" {
    for_each = lookup(each.value, "require_status_checks", false) ? [1] : []
    content {
      strict   = lookup(each.value, "strict_status_checks", true)
      contexts = lookup(each.value, "status_check_contexts", [])
    }
  }

  # Restrict Pushes (only if enabled via variable)
  dynamic "restrict_pushes" {
    for_each = each.value.restrict_pushes ? [1] : []
    content {
      blocks_creations = each.value.blocks_creations
      push_allowances  = lookup(each.value, "push_allowances", [])
    }
  }

  require_conversation_resolution = lookup(each.value, "require_conversation_resolution", true)

  lifecycle {
    precondition {
      condition     = contains(keys(var.repositories), each.value.repository_key)
      error_message = "Repository key \"${each.value.repository_key}\" provided in branch_protections key \"${each.key}\" does not exist in var.repositories."
    }
  }
}
# ===================== Organization Settings ======================
resource "github_organization_settings" "main" {
  billing_email                                                = local.organization_billing_email
  company                                                      = local.organization_company
  blog                                                         = local.organization_blog
  email                                                        = local.organization_email
  twitter_username                                             = local.organization_twitter
  location                                                     = local.organization_location
  name                                                         = local.organization_name
  description                                                  = local.organization_description
  has_organization_projects                                    = lookup(var.organization_settings, "has_organization_projects", true)
  has_repository_projects                                      = lookup(var.organization_settings, "has_repository_projects", true)
  default_repository_permission                                = lookup(var.organization_settings, "default_repository_permission", "read")
  members_can_create_repositories                              = lookup(var.organization_settings, "members_can_create_repositories", false)
  members_can_create_public_repositories                       = lookup(var.organization_settings, "members_can_create_public_repositories", false)
  members_can_create_private_repositories                      = lookup(var.organization_settings, "members_can_create_private_repositories", false)
  members_can_create_internal_repositories                     = lookup(var.organization_settings, "members_can_create_internal_repositories", false)
  members_can_create_pages                                     = lookup(var.organization_settings, "members_can_create_pages", false)
  members_can_create_public_pages                              = lookup(var.organization_settings, "members_can_create_public_pages", false)
  members_can_create_private_pages                             = lookup(var.organization_settings, "members_can_create_private_pages", false)
  members_can_fork_private_repositories                        = lookup(var.organization_settings, "members_can_fork_private_repositories", false)
  web_commit_signoff_required                                  = lookup(var.organization_settings, "web_commit_signoff_required", true)
  advanced_security_enabled_for_new_repositories               = lookup(var.organization_settings, "advanced_security_enabled_for_new_repositories", true)
  dependabot_alerts_enabled_for_new_repositories               = lookup(var.organization_settings, "dependabot_alerts_enabled_for_new_repositories", true)
  dependabot_security_updates_enabled_for_new_repositories     = lookup(var.organization_settings, "dependabot_security_updates_enabled_for_new_repositories", true)
  dependency_graph_enabled_for_new_repositories                = lookup(var.organization_settings, "dependency_graph_enabled_for_new_repositories", true)
  secret_scanning_enabled_for_new_repositories                 = lookup(var.organization_settings, "secret_scanning_enabled_for_new_repositories", true)
  secret_scanning_push_protection_enabled_for_new_repositories = lookup(var.organization_settings, "secret_scanning_push_protection_enabled_for_new_repositories", true)
}