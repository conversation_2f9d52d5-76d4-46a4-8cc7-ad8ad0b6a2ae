output "team_ids" {
  description = "Map of team slugs to their numeric IDs."
  value = merge(
    { for slug, team in github_team.level0 : slug => team.id },
    { for slug, team in github_team.level1 : slug => team.id },
    { for slug, team in github_team.level2 : slug => team.id },
    { for slug, team in github_team.level3 : slug => team.id }
  )
}

output "repository_names" {
  description = "List of managed repository names."
  value       = [for repo in github_repository.main : repo.name]
}

output "organization_settings" {
  description = "The applied GitHub organization settings."
  value       = github_organization_settings.main
}
