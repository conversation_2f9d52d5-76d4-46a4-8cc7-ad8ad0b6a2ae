# Vault KV Secrets Engine Module

This Terraform module manages a Key-Value (KV) secrets engine in HashiCorp Vault, including its configuration and secrets.

## Features

- Enables a KV secrets engine (v1 or v2) at a specified path.
- Configures KV v2 specific settings like `max_versions` and `delete_version_after`.
- Creates multiple secrets within the engine from a map variable.

## Usage Example

```hcl
module "vault_kv" {
  source = "./modules/vault_kv_secrets_engine" # Adjust path as needed

  path        = "my-kv-secrets"
  description = "KV secrets for my application"
  options     = { version = "2" }

  max_versions = 10
  delete_version_after = "720h" # 30 days

  secrets = {
    "app/config" = {
      api_key    = "supersecretapikey"
      db_password = "verysecurepassword"
    }
    "another/path" = {
      token = "sometoken"
    }
  }
}
```

## Inputs

- `path`: (Required) The path where the KV secrets engine will be enabled.
- `description`: (Optional) A human-friendly description of the secrets engine.
- `options`: (Optional) Specifies KV options. Defaults to `{ version = "2" }`.
- `secrets`: (Optional) A map of secrets to create. Keys are secret paths (relative to the mount path), and values are maps of key-value data.
- `default_lease_ttl_seconds`: (Optional) The default lease duration for tokens and secrets.
- `max_lease_ttl_seconds`: (Optional) The maximum lease duration for tokens and secrets.
- `cas_required`: (Optional) If true, the 'cas' parameter must be set on all write requests for KV v2. Defaults to `false`.
- `delete_version_after`: (Optional) Specifies the length of time before a version is deleted (e.g., "2160h"). KV v2 only.
- `max_versions`: (Optional) The number of versions to keep. KV v2 only.

## Outputs

- `mount_path`: The path where the KV secrets engine is mounted.
- `mount_accessor`: The accessor for the KV secrets engine mount.
- `secret_paths`: The full paths to the secrets created.
