# Infrastructure as Code Repository

This repository contains the Terraform and Terragrunt configuration for managing our cloud infrastructure across multiple environments.

## Structure

```
infra/
├── terraform/
│   ├── modules/                    # Reusable Terraform modules
│   │   ├── digitalocean/           # Modules specific to DigitalOcean (vpc, doks, etc.)
│   │   └── github_org/             # Module for managing GitHub organization
│   ├── live/                       # Terragrunt configurations (deployable units)
│   │   ├── shared/                 # Cross-environment resources (e.g., GitHub Org)
│   │   │   └── github/
│   │   ├── networking/             # Base network infrastructure (e.g., VPC)
│   │   └── kubernetes/             # Kubernetes cluster configuration
│   │   # Add directories for environments (dev, stg, prd) here as needed
│   └── root.hcl                    # Root Terragrunt configuration (backend, common vars)
├── scripts/                        # Helper scripts (CI, tools)
│   ├── ci/
│   └── tools/
├── docs/                           # Documentation (architecture, runbooks, standards)
│   ├── architecture/
│   ├── runbooks/
│   └── standards/
└── .github/                        # GitHub Actions workflows and templates
    └── workflows/
```

## Getting Started

### Prerequisites

- Terraform v1.3.0+ (check `required_version` in modules/root.hcl)
- Terragrunt v0.38.0+ (check Terragrunt documentation for compatibility)
- Valid cloud provider credentials (e.g., DigitalOcean API Token set as DIGITALOCEAN_TOKEN env var)
- GitHub Personal Access Token with appropriate scopes set as GITHUB_TOKEN env var (for github_org module)

### Setup & Workflow

1.  Clone this repository.
2.  Ensure necessary environment variables (like `DIGITALOCEAN_TOKEN`, `GITHUB_TOKEN`) are set.
3.  Navigate to the specific configuration directory within `terraform/live/` you want to manage (e.g., `cd terraform/live/networking`).
4.  Initialize Terragrunt (downloads modules and providers): `terragrunt init`
5.  Run `terragrunt plan` to see proposed changes for that specific configuration.
6.  Apply changes with `terragrunt apply`.

To run commands across multiple configurations (respecting dependencies):

1.  Navigate to `terraform/live/`.
2.  Run `terragrunt run-all plan` or `terragrunt run-all apply`.

## Development Guidelines

- Always work in a feature branch.
- Run `terraform fmt --recursive` from the repo root to format all Terraform/Terragrunt files.
- Run `terragrunt validate-inputs` within specific `live/` directories or `terragrunt run-all validate-inputs` from `live/`.
- Include meaningful commit messages.
- Create pull requests for review before merging to main.

## Environments

Configurations are organized within the `terraform/live/` directory.

- **`shared/`**: Contains resources shared across or foundational for multiple environments (e.g., GitHub org setup).
- **Environment-specific directories (e.g., `dev/`, `stg/`, `prd/`)** should be created within `live/` as needed. These directories would contain `terragrunt.hcl` files that potentially include common configurations from `shared/` or the root, and define environment-specific inputs.

## Documentation

Refer to the `/docs` directory for detailed documentation:

- Architecture diagrams
- Operational runbooks (including `docs/runbooks/vault-operations.md`)
- Best practices and standards

## Key Components

### HashiCorp Vault Integration

HashiCorp Vault has been integrated for centralized secrets management within our infrastructure.

- **Deployment:** Vault is deployed on DigitalOcean Kubernetes (DOKS) using its official Helm chart. This deployment is managed by a dedicated Terragrunt module located at `terraform/modules/vault-doks/`.
- **Live Configuration:**
    - The Vault Helm chart deployment is configured per environment under `terraform/live/<env>/vault/`.
    - The internal configuration of Vault (auth methods, policies, roles, secrets engines) is managed via Terragrunt under `terraform/live/<env>/vault-config/`.
- **Secrets Consumption:** Applications running in Kubernetes can consume secrets managed by Vault using the Vault CSI Provider.
    - Examples include `app-foo` and `temporal`, which have `SecretProviderClass` configurations defined in their respective live directories (`terraform/live/dev/app-foo/` and `terraform/live/dev/temporal/`).
- **Operations:** For detailed operational procedures, including unsealing, backup, and monitoring, refer to the [Vault Operations Runbook](docs/runbooks/vault-operations.md).

## Contributing

Please read our contribution guidelines before submitting changes.
