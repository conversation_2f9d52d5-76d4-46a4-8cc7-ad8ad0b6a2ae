include "root" {
  path = find_in_parent_folders("root.hcl")
}

# Define a dependency on the networking stack to get the VPC ID
dependency "networking" {
  config_path = "../networking"

  # Mock outputs for plan if needed, replace with actual values if known
  mock_outputs = {
    # Use the known VPC ID for mocking during plan, using the correct output name
    vpc_id = "1a88e2d6-adaf-4a23-933c-435d4a23d040"
  }
  mock_outputs_allowed_terraform_commands = ["plan", "validate"]
}


terraform {
  # Source the actual DOKS module
  source = "../../modules/digitalocean/doks"
}

# Define inputs for the module.
inputs = {
  cluster_name       = "verge-k8s-main"
  region             = "blr1"
  kubernetes_version = "1.32.1-do.0" # Consider using a broader range like "1.32" for flexibility
  # Use the correct output name from the networking dependency
  vpc_uuid = dependency.networking.outputs.vpc_id

  # Values for the existing node pool - Adjust if needed
  node_pool_name  = "main-pool"       # Assuming this is the name from your cluster
  node_pool_size  = "s-4vcpu-8gb-amd" # NOTE: This value is NOT in doctl output - adjust to your actual size
  node_pool_count = 3                 # NOTE: This value is NOT in doctl output - adjust to your actual count

  # Existing cluster settings - confirm these match reality
  ha           = true
  auto_upgrade = false

  # Add other inputs like tags, enable_registry_integration as needed
  tags                 = ["managed-by-terraform", "production"]
  registry_integration = true
}

# Example of reading common variables (if you set them up in root.hcl or common files)
# locals {
#   account_vars = read_terragrunt_config(find_in_parent_folders("account.hcl"))
#   region_vars  = read_terragrunt_config(find_in_parent_folders("region.hcl"))
#   environment_vars = read_terragrunt_config(find_in_parent_folders("environment.hcl"))
# } 