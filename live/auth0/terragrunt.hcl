/**
 * Auth0 Terragrunt Configuration
 * 
 * This is the parent configuration that defines shared settings
 * for all Auth0 environments (dev, stage, and prod).
 */
skip = true

# -----------------------------------------------------------------------------
# CONSTANTS AND CONFIGURATION VALUES
# -----------------------------------------------------------------------------
locals {
  # Directory paths for configuration resources
  BASE_DIR      = dirname(find_in_parent_folders("root.hcl"))
  TEMPLATES_DIR = "${local.BASE_DIR}/auth0/templates"
  ACTIONS_DIR   = "${local.BASE_DIR}/auth0/actions"
  FLOWS_DIR     = "${local.BASE_DIR}/auth0/flows"

  # Environment variable names for credentials
  ENV_AUTH0_DOMAIN        = "AUTH0_DOMAIN"
  ENV_AUTH0_CLIENT_ID     = "AUTH0_MGMT_CLIENT_ID"
  ENV_AUTH0_CLIENT_SECRET = "AUTH0_MGMT_CLIENT_SECRET"

  # Resource naming standards by environment
  FORMAT_DEV_RESOURCE   = "DEV - %s"
  FORMAT_STAGE_RESOURCE = "STAGE - %s"

  # Time-related constants (in seconds)
  ONE_HOUR_IN_SECONDS  = 3600
  ONE_DAY_IN_SECONDS   = 86400
  ONE_WEEK_IN_SECONDS  = 604800
  ONE_MONTH_IN_SECONDS = 2592000


  # Session durations (in hours)
  DEFAULT_SESSION_LIFETIME      = 168 # 7 days
  DEFAULT_IDLE_SESSION_LIFETIME = 72  # 3 days

  # Common URLs
  DEFAULT_LOGO_URL    = "https://assets.vergecloud.com/verge-logo.svg"
  DEFAULT_FAVICON_URL = "https://assets.vergecloud.com/favicon.ico"

  # Email configuration
  DEFAULT_EMAIL_SENDER = "<EMAIL>"

  APP_URL                     = try(get_env("APP_URL"), "https://vergedevelop.com")
  API_URL                     = try(get_env("API_URL"), "https://api.vergedevelop.com")
  google_oauth2_client_id     = try(get_env("TF_VAR_google_client_id"), error("Set TF_VAR_google_client_id"))
  google_oauth2_client_secret = try(get_env("TF_VAR_google_client_secret"), error("Set TF_VAR_google_client_secret"))
  # Color scheme
  PRIMARY_COLOR    = "#000000"
  BACKGROUND_COLOR = "#000000"

  EMAIL_TEMPLATES_FROM = "{{ friendly_name }} <${local.DEFAULT_EMAIL_SENDER}>"

  # Custom DB Scripts for Auth0 Connection
  DB_SCRIPTS = {
    "change_password" = file("${local.ACTIONS_DIR}/change_passowrd_custom.js")
    "create"          = file("${local.ACTIONS_DIR}/create_custom.js")
    "delete"          = file("${local.ACTIONS_DIR}/delete_custom.js")
    "get_user"        = file("${local.ACTIONS_DIR}/get_user_custom.js")
    "login"           = file("${local.ACTIONS_DIR}/login_custom.js")
    "verify"          = file("${local.ACTIONS_DIR}/verify_custom.js")
  }
}

terraform {
  # Point to the Auth0 module with correct relative path
  # The path is relative to the directory where terragrunt runs
  source = "${dirname(find_in_parent_folders("root.hcl"))}/../modules/auth0"
}

# -----------------------------------------------------------------------------
# INPUTS
# -----------------------------------------------------------------------------
inputs = {
  # --- Provider Credentials ---
  # Credentials are now sourced from environment-specific TF_VAR_* variables in child configurations.

  # --- Common Base Configuration ---
  # These are shared across all environments but can be overridden

  # Default tenant settings
  tenant = {
    picture_url                       = local.DEFAULT_LOGO_URL
    session_lifetime                  = local.DEFAULT_SESSION_LIFETIME
    idle_session_lifetime             = local.DEFAULT_IDLE_SESSION_LIFETIME
    enabled_locales                   = ["en"]
    customize_mfa_in_postlogin_action = true
    disable_acr_values_supported      = true
    flags = {
      enable_custom_domain_in_emails     = true
      enable_apis_section                = false
      enable_client_connections          = false
      mfa_show_factor_list_on_enrollment = true
    }
  }

  # Default branding settings
  branding = {
    logo_url    = local.DEFAULT_LOGO_URL
    favicon_url = local.DEFAULT_FAVICON_URL
    colors = {
      primary         = local.PRIMARY_COLOR
      page_background = local.BACKGROUND_COLOR
    }
    universal_login = {
      body = file("${local.TEMPLATES_DIR}/universal_login.html")
    }
  }

  # Branding theme configuration for Universal Login customization
  branding_theme = {
    display_name = "VergeCloud Theme"

    borders = {
      button_border_radius = 7
      button_border_weight = 1
      buttons_style        = "rounded"
      input_border_radius  = 7
      input_border_weight  = 1
      inputs_style         = "rounded"
      show_widget_shadow   = false
      widget_border_weight = 1
      widget_corner_radius = 7
    }

    colors = {
      base_focus_color          = "#467475"
      base_hover_color          = "#fde0d8"
      body_text                 = "#1F2937"
      error                     = "#dc2626"
      header                    = "#030712"
      icons                     = "#4B5563"
      input_background          = "#ffffff"
      input_border              = "#cde0e3"
      input_filled_text         = "#021213"
      input_labels_placeholders = "#6F7070"
      links_focused_components  = "#f05a28"
      primary_button            = "#042A2B"
      primary_button_label      = "#ffffff"
      secondary_button_border   = "#d1d5db"
      secondary_button_label    = "#4B5563"
      success                   = "#34A853"
      widget_background         = "#ffffff"
      widget_border             = "#E5E7EB"
    }

    fonts = {
      font_url            = ""
      links_style         = "normal"
      reference_text_size = 16

      body_text = {
        bold = false
        size = 87.5
      }

      buttons_text = {
        bold = false
        size = 100
      }

      input_labels = {
        bold = false
        size = 100
      }

      links = {
        bold = true
        size = 87.5
      }

      title = {
        bold = false
        size = 150
      }

      subtitle = {
        bold = false
        size = 87.5
      }
    }

    page_background = {
      background_color     = "#042a2b"
      background_image_url = "https://assets.vergecloud.com/LoginBackground2.svg"
      page_layout          = "center"
    }

    widget = {
      header_text_alignment = "center"
      logo_height           = 52
      logo_position         = "center"
      logo_url              = "https://assets.vergecloud.com/verge-logo.svg"
      social_buttons_layout = "bottom"
    }
  }

  # Common email templates - using file to avoid errors
  email_templates = {
    "Password Reset Email" = {
      body                      = file("${local.TEMPLATES_DIR}/pass_reset_email.html")
      enabled                   = true
      from                      = local.EMAIL_TEMPLATES_FROM
      include_email_in_redirect = false
      result_url                = "{{ application.callback_domain }}"
      syntax                    = "liquid"
      template                  = "reset_email"
      url_lifetime_in_seconds   = 432000 # 3.5 days
    },
    "Verification Email" = {
      body                      = file("${local.TEMPLATES_DIR}/verification_email.html")
      enabled                   = true
      from                      = local.EMAIL_TEMPLATES_FROM
      include_email_in_redirect = false
      result_url                = "{{ application.callback_domain }}"
      subject                   = "Verify your email"
      syntax                    = "liquid"
      template                  = "verify_email"
      url_lifetime_in_seconds   = 432000 # 12 hours
    }
  }

  # Common actions using file
  actions = {
    "enrich_token" = {
      name    = "Enrich Access Token"
      code    = file("${local.ACTIONS_DIR}/enrich_token.js")
      runtime = "node22"
      supported_triggers = [
        { id = "post-login", version = "v3" }
      ]
    },
    "verify_email" = {
      name    = "Verify Email"
      code    = templatefile("${local.ACTIONS_DIR}/verify_email.js", { FORM_ID : "ap_s9zwS5W7VYT75EgoHmufgc" })
      runtime = "node22"
      supported_triggers = [
        { id = "post-login", version = "v3" }
      ]
    },
    "mfa_check" = {
      name    = "MFA Check"
      code    = file("${local.ACTIONS_DIR}/mfa_check.js")
      runtime = "node22"
      supported_triggers = [
        { id = "post-login", version = "v3" }
      ]
    }
  }


  # Common trigger bindings - these connect actions to auth events
  # Action IDs should eventually be references to the created resources
  trigger_actions = {
    "post_login" = {
      trigger = "post-login"
      actions = [
        { display_name = "Enrich Access Token" },
        { display_name = "Verify Email" },
        { display_name = "MFA Check" }
      ]
    }
  }

  # Default connection configuration - defines how users authenticate
  connections = {
    "username_password_authentication" = {
      name                 = "Username-Password-Authentication"
      strategy             = "auth0"
      is_domain_connection = false
      realms               = ["Username-Password-Authentication"]
      options = {
        brute_force_protection = true
        password_policy        = "good"
        password_history = {
          enable = false
          size   = 5
        }
        password_no_personal_info = {
          enable = false
        }
        strategy_version = 2

        # Database connection custom scripts
        custom_scripts = local.DB_SCRIPTS

        # Authentication methods
        authentication_methods = {
          passkey = {
            enabled = true
          }
          password = {
            enabled = true
          }
        }

        # Password complexity
        password_complexity_options = {
          min_length = 8
        }

        attributes = {
          email = {
            profile_required    = true
            verification_method = "link"
            identifier = {
              active = true
            }
            signup = {
              status = "required"
              verification = {
                active = true
              }
            }
          }
        }
      }
    },

    # Google OAuth2 connection will be defined in each environment
    # with environment-specific credentials
    "google_oauth2" = {
      name     = "google-oauth2"
      strategy = "google-oauth2"
      options = {
        client_id                = local.google_oauth2_client_id
        client_secret            = local.google_oauth2_client_secret
        scopes                   = ["email", "profile"]
        set_user_root_attributes = "on_each_login"
      }
    }
  }

  # -----------------------------------------------------------------------------
  # CLIENT CONFIGURATION
  # -----------------------------------------------------------------------------
  client = {
    # Single-page application client
    "vergecloud" = {
      name     = "VergeCloud"
      app_type = "regular_web"

      # Support both production and local development URLs
      cross_origin_auth = false

      # Standard grants for SPA authentication flow
      grant_types = ["authorization_code", "implicit", "refresh_token"]
      addons      = {}

      # Extended token lifetime for development convenience
      jwt_configuration = {
        lifetime_in_seconds = local.ONE_HOUR_IN_SECONDS
      }
    }
    "account_link" = {
      name        = "auth0-account-link"
      grant_types = ["authorization_code", "client_credentials", "implicit", "refresh_token"]
      jwt_configuration = {
        lifetime_in_seconds = local.ONE_HOUR_IN_SECONDS
      }
    }
  }


  # -----------------------------------------------------------------------------
  # API/RESOURCE SERVER CONFIGURATION
  # -----------------------------------------------------------------------------
  resource_servers = {
    "vergecloud" = {
      name       = "VergeCloud"
      identifier = "VergeCloud"

      # Enable offline access for token refresh capabilities
      allow_offline_access = true

      # Extended token lifetime for development
      token_lifetime   = 3000
      enforce_policies = false
      # Define API permissions/scopes
      scopes = [
        {
          value       = "read:users"
          description = "Read user information"
        },
        {
          value       = "write:users"
          description = "Create and modify users"
        },
        {
          value       = "read:profiles"
          description = "Read user profiles"
        },
        {
          value       = "write:profiles"
          description = "Create and modify user profiles"
        }
      ]
    }
  }


  # -----------------------------------------------------------------------------
  # ORGANIZATION CONFIGURATION
  # -----------------------------------------------------------------------------

  # Organization configuration matching your existing Auth0 setup
  organizations = {
    "personal_account" = {
      name         = "personal_account"
      display_name = "Personal Accounts"

      # Optional metadata - can be used for customer context
      metadata = {
        environment = "shared"
        managed_by  = "terraform"
        created_at  = "2025"
      }

      # Organization branding (optional) - customize as needed
      branding = {
        logo_url = local.DEFAULT_LOGO_URL
        colors = {
          primary         = local.PRIMARY_COLOR
          page_background = local.BACKGROUND_COLOR
        }
      }

      # Organization connections - using user-friendly connection references
      # These connection_ids now reference the logical names from the connections variable above
      connections = [
        {
          connection_id              = "username_password_authentication" # References the database connection defined above
          assign_membership_on_login = true
          is_signup_enabled          = true # Valid for database connections
          show_as_button             = true
        },
        # Uncomment and configure when you add Google OAuth2:
        {
          connection_id              = "google_oauth2" # References the google_oauth2 connection
          assign_membership_on_login = true
          show_as_button             = true
          # Note: is_signup_enabled is automatically omitted for social connections
        }
      ]
    }
  }
}

