<!DOCTYPE html>
<html lang="{{locale}}">
  <head>
    {%- auth0:head -%}
    <style>
      body {
        background-size: cover;
        background-position: center;
        background-repeat: no-repeat;
      }
    [data-action-button-primary]:disabled,
    [data-action-button-secondary]:disabled {
            background-color: #ccc;
            color: #444;
            pointer-events: none;
        }

      .prompt-wrapper {
        position: relative;
        display: flex;
        align-items: center;
        width: 600px;
        height: 100%;
        justify-content: center;
        background-color: rgb(171,153,191);
      }
    </style>
    <title>{{ prompt.screen.texts.pageTitle }}</title>
  </head>
  <body class="_widget-auto-layout">
    {% if prompt.name == "login" or prompt.name == "signup" %}
        <div class="prompt-wrapper">
        {%- auth0:widget -%}
        </div>
    {% else %}
        {%- auth0:widget -%}
    {% endif %}
  </body>
  {% if prompt.name == "signup-id" or prompt.name == "signup" %}
  <script>
    
    function disableFormSubmitHandler(e) {
        e.preventDefault();
    }

    function disableSubmit() {
        document.querySelector('[data-form-primary]').addEventListener('submit', disableFormSubmitHandler)
        var secondaryForm = document.querySelector('[data-form-secondary]');
        if (secondaryForm) {
            secondaryForm.addEventListener('submit', disableFormSubmitHandler)
        }
        document.querySelector('[data-action-button-primary]').disabled = true;
        var secondaryButtonNodeList = document.querySelectorAll('[data-action-button-secondary]');
        for (var i = 0; i < secondaryButtonNodeList.length; i++) {
            secondaryButtonNodeList[i].disabled = true;
        }
    }

    function enableSubmit() {
        document.querySelector('[data-form-primary]').removeEventListener('submit', disableFormSubmitHandler)
        var secondaryForm = document.querySelector('[data-form-secondary]');
        if (secondaryForm) {
            secondaryForm.removeEventListener('submit', disableFormSubmitHandler)
        } document.querySelector('[data-action-button-primary]').disabled = false;
        var secondaryButtonNodeList = document.querySelectorAll('[data-action-button-secondary]');
        for (var i = 0; i < secondaryButtonNodeList.length; i++) {
            secondaryButtonNodeList[i].disabled = false;
        }
    }

    var customField = document.querySelector('#custom-field-1');
    if (customField) 
        disableSubmit();


    </script> 
    {% endif %}
</html> 


<script> function validateDateFunction() { 
agg = document.querySelector('#self-serve-subscription-agreement');   
tos = document.querySelector('#terms-of-service');   


    if (tos.checked){
        enableSubmit();
    }else {
        disableSubmit();
    }

}
</script>