skip = false
# Use the parent Terragrunt configuration
include "root" {
  path = find_in_parent_folders("root.hcl") # Assign the result to path and specify the filename
}
include "parent" {
  path           = find_in_parent_folders("")
  merge_strategy = "deep"
  expose         = true
}

# Define environment-specific locals
locals {
  environment               = "stage"
  ENABLE_EMAIL_VERIFICATION = true
  PASSWORD_MIN_LENGTH       = 6
  PASSWORD_POLICY           = "good"
  BASE_DIR                  = dirname(find_in_parent_folders("root.hcl"))
  TEMPLATES_DIR             = "${local.BASE_DIR}/auth0/templates"
  ACTIONS_DIR               = "${local.BASE_DIR}/auth0/actions"
  FLOWS_DIR                 = "${local.BASE_DIR}/auth0/flows"
  DEFAULT_LOGO_URL          = "https://assets.vergecloud.com/verge-logo.svg"
  DEFAULT_FAVICON_URL       = "https://assets.vergecloud.com/favicon.ico"

  # Stage-specific overrides
  stage_overrides = {
    # Stage-specific URLs
    app_base_url = "https://vergestage.com"
    api_base_url = "https://api.vergestage.com"

    # Stage can have some QA-related additional URLs
    additional_callbacks = [
      "https://qa.vergestage.com/callback"
    ]

    additional_allowed_logout_urls = [
      "https://qa.vergestage.com"
    ]

    # Stage email settings
    email_from_address = "<EMAIL>"
  }
  EMAIL_TEMPLATES_FROM = "{{ friendly_name }} <<EMAIL>>"
  auth0_domain         = "vergecloud-staging.eu.auth0.com"
  ENABLE_MFA           = try(tobool(get_env("ENABLE_MFA_IN_STAGE")), false)
}

# Override inputs with environment-specific values
inputs = {
  # Auth0 credentials for Stage
  auth0_domain        = local.auth0_domain
  auth0_client_id     = get_env("TF_VAR_auth0_staging_client_id")
  auth0_client_secret = get_env("TF_VAR_auth0_staging_client_secret")

  # Specify the environment for the module
  environment = local.environment

  actions = {
    "enrich_token" = {
      name = "Enrich Access Token"
    },
    "verify_email" = {
      code = templatefile("${local.ACTIONS_DIR}/verify_email.js", { FORM_ID : "ap_1WYrCrYTDuJnTybT8ZRkyk" })
    }
  }
  # Override specific settings for stage environment
  tenant = {
    picture_url         = local.DEFAULT_LOGO_URL
    friendly_name       = "VergeCloud"
    allowed_logout_urls = []

    # Stage-specific tenant settings
    flags = {
      enable_client_connections      = false
      enable_apis_section            = false
      enable_custom_domain_in_emails = true
      # Better security in stage than dev
      enable_public_signup_user_exists_error = false
      universal_login                        = true
    }
  }

  # Stage-specific client overrides
  client = {
    "vergecloud" = {
      name                          = "Vergecloud"
      organization_require_behavior = "post_login_prompt"
      organization_usage            = "require"
      callbacks = [
        "https://vergestage.com/auth/callback",
        "https://accounts.zohoportal.com/accounts/csamlresponse/***********"
      ]

      allowed_logout_urls = [
        "https://vergestage.com",
      ]
      initiate_login_uri = "https://vergestage.com/login"
      logo_uri           = local.DEFAULT_LOGO_URL
      grant_types = [
        "client_credentials",
      ]
      addons = {
        samlp = {
          audience                      = null
          authn_context_class_ref       = null
          binding                       = null
          create_upn_claim              = false
          destination                   = null
          digest_algorithm              = ""
          include_attribute_name_format = false
          issuer                        = null
          lifetime_in_seconds           = 0
          map_identities                = false
          map_unknown_claims_as_is      = false
          mappings = {
            email       = "http://schemas.xmlsoap.org/ws/2005/05/identity/claims/emailaddress"
            family_name = "User.LastName"
            given_name  = "User.FirstName"
          }
          name_identifier_format             = "urn:oasis:names:tc:SAML:1.1:nameid-format:emailAddress"
          name_identifier_probes             = ["http://schemas.xmlsoap.org/ws/2005/05/identity/claims/emailaddress"]
          passthrough_claims_with_no_mapping = false
          recipient                          = null
          sign_response                      = false
          signature_algorithm                = ""
          signing_cert                       = null
          typed_attributes                   = false
        }
      }
      # Stage requires stronger security settings
      jwt_configuration = {
        alg                 = "RS256"
        lifetime_in_seconds = 3600 # 1 hours
      }
    }
  }

  # Guardian settings for stage environment
  guardian = {
    otp    = true
    policy = local.ENABLE_MFA ? "all-applications" : "never"
    # Other stage-specific MFA settings that are more production-like
    mfa_remember_browser_lifetime = 20 # days
  }

  # Email provider settings specific to stage
  email_provider = {
    name                 = "smtp"
    enabled              = true
    default_from_address = local.stage_overrides.email_from_address
    credentials = {
      smtp_host = "smtp.zeptomail.in"
      smtp_port = 587

      smtp_user = "emailapikey"
    }
  }

  email_templates = {
    "Password Reset Email" = {
      from = "${local.EMAIL_TEMPLATES_FROM}"
    },
    "Verification Email" = {
      from = "${local.EMAIL_TEMPLATES_FROM}"
    }
  }
  # Custom domain for stage
  custom_domains = {
    "auth_vergestage_com" = {
      domain = "auth.vergestage.com"
      type   = "auth0_managed_certs"
    }
  }

  # Custom branding for stage
  branding = {
    universal_login = null
  }

  # Add connection scripts for the username_password_authentication connection
  connections = {
    "username_password_authentication" = {
      options = {
        # Less strict password requirements for development
        password_policy = local.PASSWORD_POLICY

        # Don't require usernames for simpler testing
        requires_username = false

        # Allow signup for easier onboarding in development
        disable_signup = false

        # Simplified password requirements for testing
        password_complexity_options = {
          min_length = local.PASSWORD_MIN_LENGTH
        }

        # Configurable email verification based on environment flag
        attributes = {
          email = {
            verification_method = "link"
            signupsignup = {
              verification = {
                active = local.ENABLE_EMAIL_VERIFICATION
              }
            }
          }
        }
      }
    }
  }

  resource_servers = {
    "vergecloud" = {
      token_lifetime = 300
    }
  }
}