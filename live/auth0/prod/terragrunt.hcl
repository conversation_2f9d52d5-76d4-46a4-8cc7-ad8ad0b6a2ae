skip = false
# Use the parent Terragrunt configuration
include "root" {
  path = find_in_parent_folders("root.hcl") # Assign the result to path and specify the filename
}
include "parent" {
  path           = find_in_parent_folders("")
  merge_strategy = "deep"
  expose         = true
}

# Define environment-specific locals
locals {
  environment               = "prod"
  ENABLE_EMAIL_VERIFICATION = true
  PASSWORD_MIN_LENGTH       = 6
  PASSWORD_POLICY           = "good"

  BASE_DIR            = dirname(find_in_parent_folders("root.hcl"))
  TEMPLATES_DIR       = "${local.BASE_DIR}/auth0/templates"
  ACTIONS_DIR         = "${local.BASE_DIR}/auth0/actions"
  FLOWS_DIR           = "${local.BASE_DIR}/auth0/flows"
  DEFAULT_LOGO_URL    = "https://assets.vergecloud.com/verge-logo.svg"
  DEFAULT_FAVICON_URL = "https://assets.vergecloud.com/favicon.ico"
  ENABLE_MFA          = try(tobool(get_env("ENABLE_MFA_IN_PROD")), false)

  # Production-specific overrides
  prod_overrides = {
    # Production URLs
    app_base_url = "https://panel.vergecloud.com"
    api_base_url = "https://api.vergecloud.com"

    # Production email settings
    email_from_address = "<EMAIL>"

    # Production security settings
    token_lifetime                = 3600 # 1 hour
    session_lifetime              = 168  # 72 hours
    idle_session_lifetime         = 72   # 24 hours
    mfa_remember_browser_lifetime = 15   # 15 days
  }
  auth0_domain = "vergecloud-production.eu.auth0.com"
}

# Override inputs with environment-specific values
inputs = {
  # Auth0 credentials for Prod
  auth0_domain        = local.auth0_domain
  auth0_client_id     = get_env("TF_VAR_auth0_prod_client_id")
  auth0_client_secret = get_env("TF_VAR_auth0_prod_client_secret")

  # Specify the environment for the module
  environment = local.environment

  actions = {
    "enrich_token" = {
      name = "Enrich Access Token"
    },
    "verify_email" = {
      code = templatefile("${local.ACTIONS_DIR}/verify_email.js", { FORM_ID : "ap_1WYrCrYTDuJnTybT8ZRkyk" })
    }
  }
  # Override specific settings for production environment
  tenant = {
    # No environment suffix in production
    picture_url                       = local.DEFAULT_LOGO_URL
    friendly_name                     = "VergeCloud"
    support_email                     = "<EMAIL>"
    support_url                       = "https://www.vergecloud.com/support"
    customize_mfa_in_postlogin_action = false

    # Strict production-only allowed URLs
    allowed_logout_urls = [
    ]

    # Production session settings
    session_lifetime      = local.prod_overrides.session_lifetime
    idle_session_lifetime = local.prod_overrides.idle_session_lifetime

    PASSWORD_MIN_LENGTH = 6
    PASSWORD_POLICY     = "good"
    # Production tenant flags
    flags = {
      enable_custom_domain_in_emails     = true
      mfa_show_factor_list_on_enrollment = false
      # Strict security in production
      enable_public_signup_user_exists_error = false
      disable_clickjack_protection_headers   = false
      universal_login                        = true
    }
  }

  # Production-specific client configurations
  client = {
    "vergecloud" = {
      # No environment suffix in production
      name                          = "VergeCloud"
      organization_require_behavior = "post_login_prompt"
      organization_usage            = "require"

      # Production-only callbacks
      callbacks = [
        "https://panel.vergecloud.com/api/auth/callback",
        "https://panel.vergecloud.com/auth/callback",
        "https://accounts.zohoportal.com/accounts/csamlresponse/***********",
        "https://accounts.zohoportal.in/accounts/csamlresponse/***********"
      ]

      # Production-only logout URLs
      allowed_logout_urls = [
        local.prod_overrides.app_base_url
      ]

      # Production-grade JWT settings
      jwt_configuration = {
        alg                 = "RS256"
        lifetime_in_seconds = local.prod_overrides.token_lifetime
      }
      initiate_login_uri = "${local.prod_overrides.app_base_url}/login"
      logo_uri           = local.DEFAULT_LOGO_URL

      # Production refresh token settings
      refresh_token = {
        rotation_type                = "non-rotating"
        expiration_type              = "expiring"
        leeway                       = 0
        token_lifetime               = 86400 # 1 day
        idle_token_lifetime          = 86399 # 1 day
        infinite_token_lifetime      = false
        infinite_idle_token_lifetime = true
      }
      addons = {
        samlp = {
          digest_algorithm              = ""
          create_upn_claim              = false
          lifetime_in_seconds           = 0
          typed_attributes              = false
          include_attribute_name_format = false
          mappings = {
            email       = "http://schemas.xmlsoap.org/ws/2005/05/identity/claims/emailaddress",
            family_name = "User.LastName",
            given_name  = "User.FirstName",
          }
          name_identifier_format = "urn:oasis:names:tc:SAML:1.1:nameid-format:emailAddress"
          name_identifier_probes = [
            "http://schemas.xmlsoap.org/ws/2005/05/identity/claims/emailaddress"
          ]
          signature_algorithm = ""
          issuer              = null
          signing_cert        = null
          audience            = null
        }
      }
      grant_types = [
        "client_credentials",
      ]
    }
  }

  # Production resource servers
  resource_servers = {
    "vergecloud" = {
      token_lifetime = 300
    }
  }

  # Production Guardian settings
  guardian = {
    policy                        = local.ENABLE_MFA ? "all-applications" : "never"
    phone_provider                = "auth0"
    phone_message_types           = ["sms", "voice"]
    recovery_code_enabled         = true
    mfa_remember_browser_lifetime = local.prod_overrides.mfa_remember_browser_lifetime
    push_enabled                  = true
    email_enabled                 = true
    otp                           = true
    webauthn_enabled              = true
    webauthn_platform_enabled     = true
  }

  # Production email provider
  email_provider = {
    default_from_address = local.prod_overrides.email_from_address
    enabled              = true
    name                 = "smtp"
    credentials = {
      smtp_host = "smtp.zeptomail.in"
      smtp_port = 587
      smtp_user = "emailapikey"
    }
  }

  # Custom branding for production
  branding = {
    universal_login = null
  }

  # Add connection scripts for the username_password_authentication connection
  connections = {
    "username_password_authentication" = {
      options = {
        # Less strict password requirements for development
        password_policy = local.PASSWORD_POLICY

        # Don't require usernames for simpler testing
        requires_username = false

        # Allow signup for easier onboarding in development
        disable_signup = false

        # Simplified password requirements for testing
        password_complexity_options = {
          min_length = local.PASSWORD_MIN_LENGTH
        }

        # Configurable email verification based on environment flag
        attributes = {
          email = {
            verification_method = "link"
            signupsignup = {
              verification = {
                active = local.ENABLE_EMAIL_VERIFICATION
              }
            }
          }
        }
      }
    }
  }

  # Production attack protection
  attack_protection = {
    breached_password_detection = {
      enabled                      = false
      shields                      = null
      admin_notification_frequency = null
      method                       = "standard"
    }
    brute_force_protection = {
      enabled      = true
      shields      = ["block", "user_notification"]
      mode         = "count_per_identifier_and_ip"
      max_attempts = 10 # Stricter in production
    }
    suspicious_ip_throttling = {
      enabled = true
      shields = ["block", "admin_notification"]
    }
  }

  # Production custom domains
  custom_domains = {
    "auth_vergecloud_com" = {
      domain     = "auth.vergecloud.com"
      type       = "auth0_managed_certs"
      tls_policy = "recommended"
    }
  }

  actions = {
    "verify_email" = {
      code = templatefile("${local.ACTIONS_DIR}/verify_email.js", { FORM_ID : "ap_fqNv9XVxpz5ZfKizf1aqVo" })
    }
  }

  email_templates = {
    "Password Reset Email" = {
      result_url = ""
    },
    "Verification Email" = {
      subject                 = ""
      url_lifetime_in_seconds = 43200
    }
  }
}