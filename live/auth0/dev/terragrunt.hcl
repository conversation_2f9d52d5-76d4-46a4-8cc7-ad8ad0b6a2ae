# -----------------------------------------------------------------------------
# TERRAGRUNT CONFIGURATION
# -----------------------------------------------------------------------------
# Auth0 Development Environment Configuration

skip = false
include "root" {
  path = find_in_parent_folders("root.hcl") # Assign the result to path and specify the filename
}
# Use the parent Terragrunt configuration
include "parent" {
  path           = find_in_parent_folders("")
  merge_strategy = "deep"
  expose         = true
}

# -----------------------------------------------------------------------------
# EN<PERSON>RONMENT CONSTANTS
# -----------------------------------------------------------------------------
locals {
  # Environment identifier
  ENVIRONMENT = "dev"

  BASE_DIR      = dirname(find_in_parent_folders("root.hcl"))
  TEMPLATES_DIR = "${local.BASE_DIR}/auth0/templates"
  ACTIONS_DIR   = "${local.BASE_DIR}/auth0/actions"
  FLOWS_DIR     = "${local.BASE_DIR}/auth0/flows"
  # Time-related constants (in seconds)
  DEV_TOKEN_LIFETIME        = 86400 # 24 hours
  DEV_SESSION_LIFETIME      = 168   # 14 days (hours)
  DEV_IDLE_SESSION_LIFETIME = 72    # 7 days (hours)
  DEV_MFA_REMEMBER_DAYS     = 30    # 30 days

  # Development-specific URL constants
  DEV_APP_URL       = try(get_env("DEV_APP_URL"), "https://vergedevelop.com")
  DEV_API_URL       = try(get_env("DEV_API_URL"), "https://api.vergedevelop.com")
  DEV_SUPPORT_EMAIL = "<EMAIL>"


  # Security settings for development
  DEV_PASSWORD_MIN_LENGTH      = 6      # Reduced for easier testing
  DEV_PASSWORD_POLICY          = "good" # Less strict for development
  DEV_BRUTE_FORCE_MAX_ATTEMPTS = 10     # More lenient for testing

  # Feature flags for development environment
  ENABLE_MFA                = try(tobool(get_env("ENABLE_MFA_IN_DEV")), false)
  ENABLE_EMAIL_VERIFICATION = try(tobool(get_env("ENABLE_EMAIL_VERIFICATION_IN_DEV")), true)

  # dev-specific overrides
  dev_overrides = {
    # dev-specific URLs
    app_base_url = "https://vergedevelop.com"
    api_base_url = "https://api.vergedevelop.com"

    # dev email settings
    email_from_address = "<EMAIL>"
  }
  EMAIL_TEMPLATES_FROM = "{{ friendly_name }} <<EMAIL>>"

  # Local development URLs for localhost testing
  LOCAL_URLS = {
    callbacks = [
      "https://vergedevelop.com/api/auth/callback",
      "https://vergedevelop.com/auth/callback",
      "https://verx.lat/api/auth/callback",
      "https://verge.wiki/api/auth/callback",
      "http://127.0.0.1:3000/api/auth/callback",
      "http://localhost:3000/api/auth/callback",
      "https://accounts.zohoportal.com/accounts/csamlresponse/***********",
      "http://localhost:3000/auth/callback",
      "https://*.pre.vergedev.com/auth/callback",
      "https://vergedev.com/auth/callback"
    ]
    logout = [
      "https://vergedevelop.com",
      "https://verx.lat",
      "http://127.0.0.1:3000",
      "http://localhost:3000",
      "https://verge.wiki",
      "https://*.pre.vergedev.com",
      "https://vergedev.com"
    ]
    origins = [
      "http://localhost:3000",
      "http://localhost:8080"
    ]
  }

  # SMTP settings for development environment
  DEV_SMTP = {
    host = "smtp.zeptomail.in"
    port = 587
    user = "emailapikey"
    pass = try(get_env("DEV_SMTP_PASSWORD"), "dummy-password-for-dev")
  }
  auth0_domain = "dev-4x6e01noa1cqd0js.eu.auth0.com"
}

# -----------------------------------------------------------------------------
# ENVIRONMENT-SPECIFIC CONFIGURATION
# -----------------------------------------------------------------------------
inputs = {
  # Auth0 credentials for Dev
  auth0_domain        = local.auth0_domain
  auth0_client_id     = get_env("TF_VAR_auth0_dev_client_id")
  auth0_client_secret = get_env("TF_VAR_auth0_dev_client_secret")




  # Specify the environment for the module
  environment = local.ENVIRONMENT

  # -----------------------------------------------------------------------------
  # TENANT SETTINGS
  # -----------------------------------------------------------------------------
  tenant = {
    # Add environment suffix to distinguish development tenant
    friendly_name = "VergeCloud"
    support_email = local.DEV_SUPPORT_EMAIL

    # Include both main app URL and local URLs for development
    # allowed_logout_urls = concat(
    #   [local.DEV_APP_URL],
    #   local.LOCAL_URLS.logout
    # )

    # Extended sessions for easier development and testing
    session_lifetime                              = local.DEV_SESSION_LIFETIME
    idle_session_lifetime                         = local.DEV_IDLE_SESSION_LIFETIME
    customize_mfa_in_postlogin_action             = false
    disable_acr_values_supported                  = true
    allow_organization_name_in_authentication_api = true
    # Development-specific feature flags
    flags = {
      # Enable developer-friendly features
      enable_client_connections = false
      enable_apis_section       = false

      # Disable production-only features in development
      enable_custom_domain_in_emails     = true
      mfa_show_factor_list_on_enrollment = true

      # Enable SSO for consistent authentication experience
      enable_sso = true

      # Development-only debugging options
      enable_public_signup_user_exists_error = false
      universal_login                        = true
    }
  }


  # -----------------------------------------------------------------------------
  # SECURITY SETTINGS
  # -----------------------------------------------------------------------------
  # Guardian MFA settings - conditionally enabled in development
  guardian = {
    # Toggle MFA requirement based on environment flag
    policy = local.ENABLE_MFA ? "all-applications" : "never"

    # Support SMS as the primary method for simplicity
    phone_message_types = ["sms"]
    otp                 = true
    # Enable recovery codes for account recovery testing
    recovery_code_enabled = true

    # Extended remember period for development convenience
    mfa_remember_browser_lifetime = local.DEV_MFA_REMEMBER_DAYS
  }

  # Development-focused attack protection (more permissive)
  attack_protection = {
    # Monitor for breached passwords but don't block users
    breached_password_detection = {
      enabled = true
      shields = [] # Notify but don't block in dev
    }

    # Less strict brute force protection for testing
    brute_force_protection = {
      enabled      = true
      shields      = ["block", "user_notification"] # Only notify users in dev
      max_attempts = local.DEV_BRUTE_FORCE_MAX_ATTEMPTS
    }

    # Disable IP throttling to facilitate local development
    suspicious_ip_throttling = {
      enabled = true
    }
  }

  # -----------------------------------------------------------------------------
  # CONNECTION SETTINGS
  # -----------------------------------------------------------------------------
  connections = {
    "username_password_authentication" = {
      options = {
        # Less strict password requirements for development
        password_policy = local.DEV_PASSWORD_POLICY

        # Don't require usernames for simpler testing
        requires_username = false

        # Allow signup for easier onboarding in development
        disable_signup = false

        # Simplified password requirements for testing
        password_complexity_options = {
          min_length = local.DEV_PASSWORD_MIN_LENGTH
        }

        # Configurable email verification based on environment flag
        attributes = {
          email = {
            verification_method = "link"
            signupsignup = {
              verification = {
                active = local.ENABLE_EMAIL_VERIFICATION
              }
            }
          }
        }
      }
    }
  }

  client = {
    # Single-page application client
    "vergecloud" = {
      organization_require_behavior = "post_login_prompt"
      organization_usage            = "require"

      allowed_logout_urls = concat(
        local.LOCAL_URLS.logout
      )
      app_type = "regular_web"
      callbacks = concat(
        local.LOCAL_URLS.callbacks
      )
      addons = {

      }
      cross_origin_auth  = true
      initiate_login_uri = "https://vergedevelop.com/auth/login"
      logo_uri           = "https://assets.vergecloud.com/verge-logo.svg"
      grant_types = [
        "client_credentials",
      ]
    }
    "account_link" = {
      logo_uri          = "https://assets.vergecloud.com/verge-logo.png"
      cross_origin_auth = true
    }
  }

  trigger_actions = {
  }
  # Development email provider configuration
  email_provider = {
    name                 = "smtp"
    enabled              = true
    default_from_address = local.dev_overrides.email_from_address
    credentials = {
      smtp_host = "smtp.zeptomail.in"
      smtp_port = 587

      smtp_user = "emailapikey"
    }
  }

  branding = {
    universal_login = null
  }

  email_templates = {
    "Password Reset Email" = {
      from = local.EMAIL_TEMPLATES_FROM
    },
    "Verification Email" = {
      from = local.EMAIL_TEMPLATES_FROM
    }
  }

  actions = {
    "verify_email" = {
      code = templatefile("${local.ACTIONS_DIR}/verify_email.js", { FORM_ID : "ap_s9zwS5W7VYT75EgoHmufgc" })
    }
  }
}