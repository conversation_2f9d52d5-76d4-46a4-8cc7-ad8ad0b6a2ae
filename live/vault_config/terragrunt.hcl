skip = true # Skip root configuration to avoid conflicts

# Include the root configuration
# include "root" {
#   path = find_in_parent_folders("root.hcl")
# }
# # Include bootstrap configuration for first-time setup
# include "bootstrap" {
#   path   = "${get_terragrunt_dir()}/bootstrap.hcl"
#   expose = true
# }

# # Generate a temporary kubeconfig file from the kubernetes dependency output
# generate "kubeconfig" {
#   path      = "${get_terragrunt_dir()}/.kubeconfig_temp" # Use absolute path with get_terragrunt_dir()
#   if_exists = "overwrite_terragrunt"
#   contents  = dependency.kubernetes.outputs.kube_config
# }

# # Configure the Terraform module source
# terraform {
#   source = "../../modules/vault_config"

#   before_hook "validate_vault_token" {
#     commands = ["apply", "plan", "import"]
#     execute  = ["bash", "-c", "if [ -z \"$VAULT_SETUP_TOKEN\" ]; then echo 'Error: VAULT_SETUP_TOKEN must be set'; exit 1; fi"]
#   }

#   before_hook "before_hook" {
#     commands = ["apply", "plan", "import"]
#     # Use absolute paths with get_terragrunt_dir() to ensure correct file locations
#     execute = ["bash", "-c", "export VAULT_BOOTSTRAP_MODE=${get_env("VAULT_BOOTSTRAP_MODE", "false")} && export DOKS_KUBECONFIG_PATH='${get_terragrunt_dir()}/.kubeconfig_temp' && source '${get_terragrunt_dir()}/../_script/start_service.sh'"]
#   }

#   after_hook "after_hook" {
#     commands = ["apply", "plan", "import"]
#     # Use absolute paths with get_terragrunt_dir() to ensure correct file locations
#     execute      = ["bash", "-c", "export VAULT_BOOTSTRAP_MODE=${get_env("VAULT_BOOTSTRAP_MODE", "false")} && export DOKS_KUBECONFIG_PATH='${get_terragrunt_dir()}/.kubeconfig_temp' && '${get_terragrunt_dir()}/../_script/stop_service.sh'; rm -f '${get_terragrunt_dir()}/.kubeconfig_temp'"]
#     run_on_error = true
#   }

#   # Set environment variables for the Terraform process
#   extra_arguments "vault_env" {
#     commands = ["apply", "plan", "import", "refresh", "destroy"]
#     env_vars = {
#       VAULT_ADDR  = "http://localhost:8200"
#       VAULT_TOKEN = get_env("VAULT_SETUP_TOKEN", "")
#     }
#   }
# }

# # Note: Provider configuration is handled by the vault_config module
# # No need to generate providers.tf as it would conflict with module's configuration

# # -----------------------------------------------------------------------------
# # VAULT CONFIGURATION CONSTANTS
# # -----------------------------------------------------------------------------
# locals {
#   VAULT_NAMESPACE    = "vault"
#   VAULT_SERVICE_NAME = "localhost" # Internal cluster service name
#   VAULT_PORT         = 8200

#   # Get environment from path component or default to dev
#   # Terragrunt can't access terraform.workspace directly
#   environment_path = basename(dirname(get_terragrunt_dir()))
#   environment      = contains(["dev", "prod", "staging"], local.environment_path) ? local.environment_path : "dev"

#   # Use HTTPS only in production
#   VAULT_PROTOCOL = local.environment == "prod" ? "https" : "http"

#   # Default policies for different environments
#   environment_policies = {
#     dev = {
#       # Development policies - more permissive for testing
#       "dev-admin-policy" = {
#         policy = <<-EOT
#           # Admin access for development
#           path "*" {
#             capabilities = ["create", "read", "update", "delete", "list", "sudo"]
#           }
#         EOT
#       }
#       "dev-app-policy" = {
#         policy = <<-EOT
#           # Application access for development
#           path "secret/data/dev/*" {
#             capabilities = ["read", "list"]
#           }
#           path "secret/metadata/dev/*" {
#             capabilities = ["read", "list"]
#           }
#         EOT
#       }
#     }

#     prod = {
#       # Production policies - restrictive and secure
#       "prod-app-read-policy" = {
#         policy = <<-EOT
#           # Read-only access to production app secrets
#           path "secret/data/prod/app/*" {
#             capabilities = ["read"]
#           }
#         EOT
#       }
#       "prod-admin-policy" = {
#         policy = <<-EOT
#           # Limited admin access for production
#           path "auth/*" {
#             capabilities = ["create", "read", "update", "delete", "list"]
#           }
#           path "secret/data/prod/*" {
#             capabilities = ["create", "read", "update", "delete", "list"]
#           }
#           path "sys/policies/acl/*" {
#             capabilities = ["create", "read", "update", "delete", "list"]
#           }
#         EOT
#       }
#     }
#   }
# }

# # Define inputs for the vault_config module
# inputs = {
#   # Environment configuration
#   environment = local.environment

#   # Vault connection configuration
#   vault_address               = "${local.VAULT_PROTOCOL}://${local.VAULT_SERVICE_NAME}:${local.VAULT_PORT}"
#   vault_internal_service_name = local.VAULT_SERVICE_NAME
#   vault_internal_port         = local.VAULT_PORT
#   vault_protocol              = local.VAULT_PROTOCOL
#   vault_auth_method           = "token" # Use token auth for initial setup, switch to kubernetes later
#   use_k8s_sa_token            = false   # Not running inside Kubernetes pod
#   k8s_service_account_jwt     = ""      # Will be set during initial setup
#   terraform_k8s_role_name     = "terraform-role"
#   skip_tls_verify             = local.environment != "prod" # Must be false in production

#   # Kubernetes provider configuration
#   cluster_endpoint       = dependency.kubernetes.outputs.cluster_endpoint
#   cluster_ca_certificate = dependency.kubernetes.outputs.cluster_ca_certificate
#   client_token           = dependency.kubernetes.outputs.client_token

#   # Kubernetes authentication configuration
#   kubernetes_auth_config = {
#     path                              = "kubernetes"
#     kubernetes_host                   = dependency.kubernetes.outputs.cluster_endpoint
#     kubernetes_ca_cert                = dependency.kubernetes.outputs.cluster_ca_certificate
#     issuer                            = "https://kubernetes.default.svc.cluster.local"
#     disable_iss_validation            = false
#     disable_local_ca_jwt              = false
#     use_annotations_as_alias_metadata = true # Use annotations for better entity identification
#   }

#   # Service account configuration for Vault authentication
#   service_account_config = {
#     create_service_account = true
#     name                   = "vault-auth"
#     namespace              = local.VAULT_NAMESPACE
#     labels = {
#       "app.kubernetes.io/name"      = "vault-auth"
#       "app.kubernetes.io/component" = "authentication"
#       "app.kubernetes.io/part-of"   = "vault"
#     }
#     annotations = {
#       "vault.hashicorp.com/auth-path" = "auth/kubernetes"
#     }
#   }

#   # Kubernetes authentication roles with enhanced security
#   kubernetes_auth_roles = {
#     "terraform-role" = {
#       bound_service_account_names      = ["vault-auth"]
#       bound_service_account_namespaces = [local.VAULT_NAMESPACE]
#       token_policies                   = ["terraform-policy", local.environment == "dev" ? "dev-admin-policy" : "prod-admin-policy"]
#       token_ttl                        = local.environment == "prod" ? 1800 : 3600 # 30 min in prod, 1 hour in dev
#       token_max_ttl                    = local.environment == "prod" ? 3600 : 7200 # 1 hour in prod, 2 hours in dev
#       token_bound_cidrs                = []                                        # Empty for internal cluster access
#       token_type                       = "service"
#       token_no_default_policy          = true                 # Don't include default policy for admin role
#       token_num_uses                   = 0                    # Unlimited uses within TTL
#       alias_name_source                = "serviceaccount_uid" # More secure than name
#     }

#     "application-role" = {
#       bound_service_account_names      = ["app-service-account"]
#       bound_service_account_namespaces = ["default", "app"]
#       token_policies                   = [local.environment == "dev" ? "dev-app-policy" : "prod-app-read-policy"]
#       token_ttl                        = 900  # 15 minutes
#       token_max_ttl                    = 1800 # 30 minutes
#       token_bound_cidrs                = []   # Empty for internal cluster access
#       token_type                       = "service"
#       token_num_uses                   = 0 # Unlimited uses within TTL
#     }
#   }

#   # Vault policies configuration
#   vault_policies = merge(
#     local.environment_policies[local.environment],
#     {
#       # Base terraform policy for managing Vault
#       "terraform-policy" = {
#         policy = <<-EOT
#           # Allow managing authentication methods
#           path "auth/*" {
#             capabilities = ["create", "read", "update", "delete", "list", "sudo"]
#           }

#           # Allow managing secrets engines
#           path "sys/mounts/*" {
#             capabilities = ["create", "read", "update", "delete", "list"]
#           }

#           # Allow managing policies
#           path "sys/policies/acl/*" {
#             capabilities = ["create", "read", "update", "delete", "list"]
#           }

#           # Allow managing audit devices
#           path "sys/audit/*" {
#             capabilities = ["create", "read", "update", "delete", "list", "sudo"]
#           }

#           # Allow reading system configuration
#           path "sys/config/*" {
#             capabilities = ["read"]
#           }

#           # Allow managing KV secrets
#           path "secret/*" {
#             capabilities = ["create", "read", "update", "delete", "list"]
#           }
#         EOT
#       }
#     }
#   )

#   # Configuration for handling existing Vault resources
#   # Set to true if Vault is already configured (e.g., via Helm)
#   vault_already_configured = true

#   # Import existing resources instead of creating them
#   import_existing_resources = local.environment == "dev" ? true : false

#   # Secrets engines configuration
#   secrets_engines = {
#     # KV v2 secrets engine for application secrets
#     "secret" = {
#       type        = "kv-v2"
#       path        = "secret"
#       description = "KV v2 secrets engine for application configuration"
#       options     = {}
#     }

#     # Database secrets engine (if you need dynamic database credentials)
#     # "database" = {
#     #   type        = "database"
#     #   path        = "database"
#     #   description = "Database secrets engine for dynamic credentials"
#     #   options     = {}
#     # }
#   }

#   # Database connections (uncomment if using database secrets engine)
#   # database_connections = {
#   #   "postgresql-prod" = {
#   #     plugin_name    = "postgresql-database-plugin"
#   #     connection_url = "postgresql://{{username}}:{{password}}@postgres.example.com:5432/mydb?sslmode=require"
#   #     allowed_roles  = ["readonly", "readwrite"]
#   #     username       = "vault_user"
#   #     password       = "secure_password" # Use secure method to provide this
#   #   }
#   # }

#   # Database roles (uncomment if using database secrets engine)
#   # database_roles = {
#   #   "readonly" = {
#   #     db_name             = "postgresql-prod"
#   #     creation_statements = ["CREATE ROLE \"{{name}}\" WITH LOGIN PASSWORD '{{password}}' VALID UNTIL '{{expiration}}'; GRANT SELECT ON ALL TABLES IN SCHEMA public TO \"{{name}}\";"]
#   #     default_ttl         = 3600
#   #     max_ttl             = 7200
#   #   }
#   # }

#   # Audit devices configuration - environment-aware
#   audit_devices = local.environment == "prod" ? {
#     # Production: Multiple audit devices for redundancy
#     "file" = {
#       type = "file"
#       path = "file"
#       options = {
#         file_path = "/vault/logs/audit.log"
#         log_raw   = "false" # Don't log sensitive data
#       }
#       description = "File audit device for logging Vault operations"
#     },
#     "syslog" = {
#       type = "syslog"
#       path = "syslog"
#       options = {
#         facility = "AUTH"
#         tag      = "vault"
#         log_raw  = "false" # Don't log sensitive data
#       }
#       description = "Syslog audit device for redundant logging"
#     }
#     } : {
#     # Development: Only file audit device (syslog not available in containers)
#     "file" = {
#       type = "file"
#       path = "file"
#       options = {
#         file_path = "/vault/logs/audit.log"
#         log_raw   = "false" # Don't log sensitive data
#       }
#       description = "File audit device for logging Vault operations"
#     }
#   }

#   # Require multiple audit devices only in production
#   require_multiple_audit_devices = local.environment == "prod"

#   # Enterprise namespace (only if using Vault Enterprise)
#   # vault_namespace = "admin"

#   # Auto-unseal configuration
#   auto_unseal_config = {
#     enabled    = local.environment == "prod" # Enable in production
#     provider   = "transit"                   # Use transit for auto-unseal
#     key_name   = "autounseal"
#     mount_path = "transit"
#   }

#   # Default token TTLs
#   default_token_ttl = local.environment == "prod" ? 1800 : 3600 # 30 min in prod, 1 hour in dev

#   # Additional configuration
#   enable_audit_devices = true

#   # Tags for resource management
#   tags = {
#     Environment = local.environment
#     ManagedBy   = "terragrunt"
#     Purpose     = "vault-configuration"
#     Team        = "platform"
#     SecLevel    = local.environment == "prod" ? "high" : "standard"
#   }
# }

# # Dependencies
# dependency "kubernetes" {
#   config_path = "../kubernetes"

#   # Mock outputs for planning
#   mock_outputs = {
#     cluster_endpoint       = "https://mock-cluster-endpoint"
#     cluster_ca_certificate = "bW9jay1jYS1jZXJ0"
#     client_token           = "mock-token"
#     # Ensure kube_config is mocked if you run 'plan' without the actual dependency being applied
#     kube_config = "apiVersion: v1\\nclusters: []\\ncontexts: []\\ncurrent-context: \\\"\\\"\\nkind: Config\\npreferences: {}\\nusers: []"
#   }
#   mock_outputs_allowed_terraform_commands = ["plan", "validate"]
# }

# dependency "helm" {
#   config_path = "../helm"

#   # Mock outputs for planning
#   mock_outputs = {
#     vault_status = "deployed"
#   }
#   mock_outputs_allowed_terraform_commands = ["plan", "validate"]

#   # Ensure Vault is deployed before configuring it
#   skip_outputs = false
# }
