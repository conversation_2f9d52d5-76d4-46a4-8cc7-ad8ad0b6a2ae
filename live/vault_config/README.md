# Vault Configuration

This directory contains Terragrunt configuration for managing HashiCorp Vault's internal configuration using Terraform. The Vault cluster itself is deployed via Helm chart (see `../helm/`), and this configuration manages the internal Vault resources like authentication methods, policies, and secrets engines.

## Overview

### What This Configures

- **Authentication Methods**: Kubernetes auth backend for secure cluster-based authentication
- **Service Accounts**: RBAC and service accounts for Vault authentication
- **Policies**: Role-based access control policies for different use cases
- **Secrets Engines**: KV v2 for application secrets, optional database secrets
- **Audit Devices**: File-based audit logging for compliance

### Architecture

```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   Terragrunt    │───▶│  Vault Config   │───▶│   Vault Helm    │
│   (This Dir)    │    │    Module       │    │     Chart       │
└─────────────────┘    └─────────────────┘    └─────────────────┘
        │                        │                        │
        │                        │                        │
        ▼                        ▼                        ▼
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│  Vault Policies │    │   Auth Methods  │    │  Vault Server   │
│  Secret Engines │    │  Service Accts  │    │   (Dev Mode)    │
│  Audit Devices  │    │   RBAC Rules    │    │      UI         │
└─────────────────┘    └─────────────────┘    └─────────────────┘
```

## Prerequisites

1. **Vault Deployed**: The Vault Helm chart must be deployed first (`../helm/`)
2. **Kubernetes Access**: Valid kubeconfig and cluster access
3. **Vault Access**: Vault should be accessible at the internal cluster address
4. **Initial Token**: For first-time setup, you'll need a root token

## Bootstrapping Vault

For initial setup, follow this procedure to break the circular dependency:

1. **Run the bootstrap helper**:
   ```bash
   ./bootstrap-vault.sh
   ```

2. **Deploy Vault with Helm first**:
   ```bash
   cd ../helm
   terragrunt apply
   ```

3. **Initialize Vault after deployment**:
   ```bash
   # In terminal 1
   kubectl port-forward service/vault 8200:8200 -n vault
   
   # In terminal 2
   export VAULT_ADDR="http://localhost:8200"
   vault operator init
   ```

4. **Save the unseal keys and root token safely**

5. **Configure Vault with bootstrap mode**:
   ```bash
   export VAULT_SETUP_TOKEN="your_root_token_from_init"
   export VAULT_BOOTSTRAP_MODE=true
   cd ../vault_config
   terragrunt apply
   ```

6. **Future runs can be performed without bootstrap mode**:
   ```bash
   unset VAULT_BOOTSTRAP_MODE
   export VAULT_SETUP_TOKEN="your_root_token"
   terragrunt plan
   ```

## Configuration

### Environment-Specific Settings

The configuration automatically adapts based on the `environment` local variable:

- **Development**: More permissive policies for testing and development
- **Production**: Restrictive policies following principle of least privilege

### Authentication Methods

#### Kubernetes Authentication
- **Path**: `auth/kubernetes`
- **Purpose**: Secure authentication for pods running in the cluster
- **Roles**:
  - `terraform-role`: For this Terraform configuration
  - `application-role`: For application pods

#### Service Accounts
- **Name**: `vault-auth`
- **Namespace**: `vault`
- **Purpose**: Provides identity for Terraform to authenticate with Vault

### Policies

#### Development Environment
- `dev-admin-policy`: Full access for development testing
- `dev-app-policy`: Read access to development secrets

#### Production Environment
- `prod-admin-policy`: Limited administrative access
- `prod-app-read-policy`: Read-only access to production application secrets
- `terraform-policy`: Permissions for managing Vault configuration

### Secrets Engines

#### KV v2 Engine
- **Path**: `secret/`
- **Purpose**: Store application configuration and secrets
- **Structure**: 
  - `secret/dev/`: Development secrets
  - `secret/prod/`: Production secrets
  - `secret/shared/`: Shared configuration

#### Database Engine (Optional)
- **Path**: `database/`
- **Purpose**: Dynamic database credentials
- **Status**: Commented out by default

## Usage

### Initial Deployment

1. **Ensure Vault is Running**:
   ```bash
   kubectl get pods -n vault
   ```

2. **Set Environment Variables**:
   ```bash
   export VAULT_ADDR="http://vault.vault.svc.cluster.local:8200"
   export VAULT_TOKEN="your-initial-root-token"  # For first-time setup only
   ```

3. **Deploy Configuration**:
   ```bash
   cd /Users/<USER>/GolandProjects/infra/live/vault_config
   terragrunt plan
   terragrunt apply
   ```

### Accessing Vault

#### From Inside the Cluster
```bash
# Port forward to access Vault UI
kubectl port-forward -n vault svc/vault 8200:8200

# Access UI at http://localhost:8200
```

#### Using kubectl exec
```bash
# Get a shell in the Vault pod
kubectl exec -it -n vault vault-0 -- sh

# Inside the pod
export VAULT_ADDR="http://127.0.0.1:8200"
vault status
```

### Managing Secrets

#### Using Vault CLI
```bash
# Write a secret
vault kv put secret/dev/myapp database_url="postgresql://..." api_key="..."

# Read a secret
vault kv get secret/dev/myapp

# List secrets
vault kv list secret/dev/
```

#### Using Kubernetes Authentication
```bash
# Get service account token
SA_TOKEN=$(kubectl get secret vault-auth-token -n vault -o jsonpath='{.data.token}' | base64 -d)

# Login with Kubernetes auth
vault write auth/kubernetes/login role=application-role jwt="$SA_TOKEN"
```

## Security Considerations

### Development Mode Warning
⚠️ **The current Vault deployment is in development mode**, which means:
- Data is stored in memory (not persistent)
- TLS is disabled
- Root token is automatically generated
- **Not suitable for production**

### Production Readiness Checklist
- [ ] Disable development mode in Helm chart
- [ ] Configure persistent storage
- [ ] Enable TLS/HTTPS
- [ ] Implement proper unsealing mechanism
- [ ] Configure backup and disaster recovery
- [ ] Enable additional audit devices
- [ ] Implement proper secret rotation policies

### Best Practices
1. **Principle of Least Privilege**: Grant minimal required permissions
2. **Token TTL**: Use short-lived tokens with appropriate renewal
3. **Audit Logging**: Enable comprehensive audit logging
4. **Secret Rotation**: Implement regular secret rotation
5. **Network Security**: Limit network access to Vault

## Troubleshooting

### Common Issues

#### Vault Not Accessible
```bash
# Check Vault pod status
kubectl get pods -n vault

# Check Vault logs
kubectl logs -n vault vault-0

# Verify service
kubectl get svc -n vault
```

#### Authentication Failures
```bash
# Check Kubernetes auth configuration
vault auth list
vault read auth/kubernetes/config

# Verify service account
kubectl get serviceaccount vault-auth -n vault
kubectl describe serviceaccount vault-auth -n vault
```

#### Permission Denied
```bash
# Check current token capabilities
vault token capabilities <path>

# List policies
vault policy list

# Read specific policy
vault policy read <policy-name>
```

### Getting Help

1. **Vault Status**: `vault status`
2. **Auth Methods**: `vault auth list`
3. **Policies**: `vault policy list`
4. **Secrets Engines**: `vault secrets list`
5. **Audit Devices**: `vault audit list`

## Related Documentation

- [Vault Operations Runbook](../../docs/runbooks/vault-operations.md)
- [Terraform Standards](../../docs/standards/terraform-standards.md)
- [Helm Chart Configuration](../helm/README.md)
- [HashiCorp Vault Documentation](https://developer.hashicorp.com/vault)

## Support

For issues or questions:
1. Check the troubleshooting section above
2. Review Vault logs and status
3. Consult the HashiCorp Vault documentation
4. Contact the platform team
