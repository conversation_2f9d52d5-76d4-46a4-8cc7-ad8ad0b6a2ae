include "root" {
  path = find_in_parent_folders("root.hcl")
}

terraform {
  source = "../../modules/digitalocean/database"
}

locals {
  # Define clusters with their configurations
  dev_cluster         = "db-postgresql-blr1-dev"
  prod_cluster        = "prod-vergecloud-db-postgresql-blr1-37804"
  log_forwarder_kafka = "log-forwarder-kafka-blr1"
  cdn_logs_errors_waf = "cdn-logs-errors-waf-db-opensearch-blr1"

  # Kafka topics for log_forwarder_kafka cluster
  KAFKA_TOPICS = [
    { name = "ahc-event-log", partition_count = 3, replication_factor = 2 },
    { name = "cdn-cp-traffic-logs-access-capnp", partition_count = 3, replication_factor = 2 },
    { name = "cdn-logs-access", partition_count = 3, replication_factor = 2 },
    { name = "cdn-logs-access-capnp", partition_count = 3, replication_factor = 2 },
    { name = "cdn-logs-detmit", partition_count = 3, replication_factor = 2 },
    { name = "cdn-logs-error", partition_count = 3, replication_factor = 2 },
    { name = "eventlog", partition_count = 3, replication_factor = 2 },
    { name = "purge", partition_count = 3, replication_factor = 2 },
    { name = "redins", partition_count = 3, replication_factor = 2 },
    { name = "sample", partition_count = 3, replication_factor = 2 },
    { name = "smartrouting-eventlog", partition_count = 3, replication_factor = 2 },
    { name = "tcp-proxy-access", partition_count = 3, replication_factor = 2 },
    { name = "waflog", partition_count = 3, replication_factor = 2 },
    { name = "workers-user", partition_count = 3, replication_factor = 2 }
  ]
}

inputs = {
  # Clusters
  clusters = {
    "${local.dev_cluster}" = {
      engine     = "pg"
      node_count = 1
      region     = "blr1"
      size       = "db-s-1vcpu-1gb"
      tags       = ["database", "dev", "development", "stage", "staging", "test"]
      version    = "16"
    }

    "${local.prod_cluster}" = {
      engine     = "pg"
      node_count = 1
      region     = "blr1"
      size       = "db-amd-1vcpu-1gb"
      tags       = ["database", "production"]
      version    = "16"
    }

    "${local.log_forwarder_kafka}" = {
      engine     = "kafka"
      node_count = 3
      region     = "blr1"
      size       = "db-s-2vcpu-2gb"
      tags       = ["kafka", "log-forwarder", "production"]
      version    = "3.8"
    }

    "${local.cdn_logs_errors_waf}" = {
      engine     = "opensearch"
      node_count = 1
      region     = "blr1"
      size       = "db-s-4vcpu-8gb"
      tags       = ["opensearch", "cdn-logs"]
      version    = "2"
    }
  }

  # Users
  users = merge(
    {
      for u in [
        { name = "doadmin", cluster = local.dev_cluster },
        { name = "admin", cluster = local.dev_cluster },
        { name = "app", cluster = local.dev_cluster },
        { name = "app-stage", cluster = local.dev_cluster },
        { name = "dev-team", cluster = local.dev_cluster },
        { name = "doadmin", cluster = local.prod_cluster },
        { name = "readonly", cluster = local.dev_cluster },
        { name = "readonly", cluster = local.prod_cluster },
        { name = "stage-team", cluster = local.dev_cluster },
        { name = "temporal", cluster = local.dev_cluster },
        { name = "unleash", cluster = local.dev_cluster },
        { name = "app", cluster = local.prod_cluster },
        { name = "doadmin", cluster = local.log_forwarder_kafka, settings = {
          acls = [
            { topic = "*", permission = "admin" }
          ]
        } },
        { name = "admin", cluster = local.log_forwarder_kafka, settings = {
          acls = [
            { topic = "*", permission = "admin" }
          ]
        } },
        { name = "app", cluster = local.log_forwarder_kafka, settings = {
          acls = [
            { topic = "*", permission = "produceconsume" }
          ]
        } },
        { name = "app-consumer", cluster = local.log_forwarder_kafka, settings = {
          acls = [
            { topic = "*", permission = "consume" }
          ]
        } }
        ] : "${u.cluster}_${u.name}" => {
        cluster  = u.cluster
        username = u.name
        settings = try(u.settings, null)
      }
    },
    {
      # ...other user definitions if present...
    }
  )

  # Databases
  databases = {
    for d in [
      { name = "defaultdb", cluster = local.dev_cluster },
      { name = "temporal", cluster = local.dev_cluster },
      { name = "temporal_visibility", cluster = local.dev_cluster },
      { name = "unleash", cluster = local.dev_cluster },
      { name = "verge", cluster = local.dev_cluster },
      { name = "verge-edge", cluster = local.dev_cluster },
      { name = "defaultdb", cluster = local.prod_cluster },
      { name = "verge", cluster = local.prod_cluster }
      ] : "${d.cluster}_${d.name}" => {
      cluster = d.cluster
      db_name = d.name
    }
  }

  firewall_rules = {
    # Trusted sources for dev cluster
    "${local.dev_cluster}" = [
      { type = "ip_addr", value = "*************" },
      { type = "ip_addr", value = "************" },
      { type = "ip_addr", value = "*************" },
      { type = "ip_addr", value = "**********/20" },
      { type = "ip_addr", value = "***************" },
      { type = "droplet", value = "485910291" },
      { type = "ip_addr", value = "***********" },
      { type = "droplet", value = "503673013" },
      { type = "ip_addr", value = "*************" },
      { type = "droplet", value = "503874394" },
      { type = "ip_addr", value = "*************" },
      { type = "ip_addr", value = "**************" },
      { type = "ip_addr", value = "**************" },
      { type = "k8s", value = "dde36ffc-e493-4270-b18d-8ef81a354ef4" },
      { type = "ip_addr", value = "*************" },
      { type = "droplet", value = "489286242" },
      { type = "droplet", value = "489456639" },
      { type = "ip_addr", value = "*************" },
      { type = "droplet", value = "479705539" },
      { type = "ip_addr", value = "**************" },
      { type = "droplet", value = "488696295" },
      { type = "ip_addr", value = "*************" },
      { type = "ip_addr", value = "*************" },
      { type = "droplet", value = "498643417" }
    ]
    # Trusted sources for prod cluster
    "${local.prod_cluster}" = [
      { type = "ip_addr", value = "*************" },
      { type = "ip_addr", value = "************" },
      { type = "ip_addr", value = "*************" },
      { type = "ip_addr", value = "**********/20" },
      { type = "ip_addr", value = "***************" },
      { type = "droplet", value = "485910291" },
      { type = "ip_addr", value = "***********" },
      { type = "droplet", value = "503673013" },
      { type = "ip_addr", value = "*************" },
      { type = "droplet", value = "503874394" },
      { type = "ip_addr", value = "*************" },
      { type = "ip_addr", value = "**************" },
      { type = "ip_addr", value = "**************" },
      { type = "k8s", value = "dde36ffc-e493-4270-b18d-8ef81a354ef4" },
      { type = "ip_addr", value = "*************" },
      { type = "droplet", value = "489286242" },
      { type = "droplet", value = "489456639" },
      { type = "ip_addr", value = "*************" },
      { type = "droplet", value = "479705539" },
      { type = "ip_addr", value = "**************" },
      { type = "droplet", value = "488696295" },
      { type = "ip_addr", value = "*************" },
      { type = "ip_addr", value = "*************" },
      { type = "droplet", value = "498643417" }
    ]
    # Trusted sources for log_forwarder_kafka cluster (existing rules remain unchanged)
    "${local.log_forwarder_kafka}" = [
      { type = "tag", value = "log-forwarder" },
      { type = "ip_addr", value = "***********" },
      { type = "ip_addr", value = "***********" },
      { type = "ip_addr", value = "*************" },
      { type = "tag", value = "k8s" },
      { type = "tag", value = "production" },
      { type = "ip_addr", value = "**************" },
      { type = "ip_addr", value = "***************" }, # 5350:lax-i3d
      { type = "ip_addr", value = "***************" }, # 5850:ewr-i3d
      { type = "ip_addr", value = "**************" },  # 6250:rtm-i3d
      { type = "ip_addr", value = "*************" },   # 9050:sao-i3d
      { type = "ip_addr", value = "*************" },   # 4250:dxb-i3d
      { type = "ip_addr", value = "**************" },  # 6150:fra-i3d
      { type = "ip_addr", value = "***************" }, # 7150:sin-i3d
      { type = "ip_addr", value = "***********" },     # 7300:bom-vlt
      { type = "ip_addr", value = "*************" },   # 7400:del-vlt
      { type = "ip_addr", value = "**************" },  # 7500:blr-vlt
      { type = "ip_addr", value = "**************" },  # Mumbai
      { type = "ip_addr", value = "**************" },  # Lucknow
      { type = "ip_addr", value = "**************" },  # Kolkata
      { type = "ip_addr", value = "***************" }, # Hyderabad
      { type = "ip_addr", value = "**************" },  # Noida
      { type = "ip_addr", value = "**************" },  # Patna
      { type = "ip_addr", value = "**************" },  # BLR
      { type = "ip_addr", value = "*************" }    # Test DO VPS
    ]
    # Trusted sources for cdn_logs_errors_waf cluster
    "${local.cdn_logs_errors_waf}" = [
      { type = "k8s", value = "dde36ffc-e493-4270-b18d-8ef81a354ef4" },
      { type = "ip_addr", value = "**********/20" },
      { type = "ip_addr", value = "***********" },
      { type = "ip_addr", value = "**********/16" }
    ]
  }

  kafka_topics = {
    for topic in local.KAFKA_TOPICS : "${local.log_forwarder_kafka}_${topic.name}" => {
      cluster            = local.log_forwarder_kafka
      name               = topic.name
      partition_count    = topic.partition_count
      replication_factor = topic.replication_factor
    }
  }
}
