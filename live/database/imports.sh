#!/bin/bash
# Auto-generated import commands
terragrunt --working-dir=. import "digitalocean_database_cluster.clusters[\"cdn-logs-errors-waf-db-opensearch-blr1\"]" "151adbdf-1a34-4369-9b41-044463950f29"
terragrunt --working-dir=. import "digitalocean_database_user.users[\"cdn-logs-errors-waf-db-opensearch-blr1_admin\"]" "151adbdf-1a34-4369-9b41-044463950f29,admin"
terragrunt --working-dir=. import "digitalocean_database_user.users[\"cdn-logs-errors-waf-db-opensearch-blr1_doadmin\"]" "151adbdf-1a34-4369-9b41-044463950f29,doadmin"
terragrunt --working-dir=. import "digitalocean_database_firewall.firewall_rules[\"cdn-logs-errors-waf-db-opensearch-blr1\"]" "151adbdf-1a34-4369-9b41-044463950f29"
terragrunt --working-dir=. import "digitalocean_database_cluster.clusters[\"db-postgresql-blr1-dev\"]" "1d021c35-9e0c-42f7-8cb6-2d15d15811d0"
terragrunt --working-dir=. import "digitalocean_database_user.users[\"db-postgresql-blr1-dev_admin\"]" "1d021c35-9e0c-42f7-8cb6-2d15d15811d0,admin"
terragrunt --working-dir=. import "digitalocean_database_user.users[\"db-postgresql-blr1-dev_app\"]" "1d021c35-9e0c-42f7-8cb6-2d15d15811d0,app"
terragrunt --working-dir=. import "digitalocean_database_user.users[\"db-postgresql-blr1-dev_app-stage\"]" "1d021c35-9e0c-42f7-8cb6-2d15d15811d0,app-stage"
terragrunt --working-dir=. import "digitalocean_database_user.users[\"db-postgresql-blr1-dev_dev-team\"]" "1d021c35-9e0c-42f7-8cb6-2d15d15811d0,dev-team"
terragrunt --working-dir=. import "digitalocean_database_user.users[\"db-postgresql-blr1-dev_doadmin\"]" "1d021c35-9e0c-42f7-8cb6-2d15d15811d0,doadmin"
terragrunt --working-dir=. import "digitalocean_database_user.users[\"db-postgresql-blr1-dev_readonly\"]" "1d021c35-9e0c-42f7-8cb6-2d15d15811d0,readonly"
terragrunt --working-dir=. import "digitalocean_database_user.users[\"db-postgresql-blr1-dev_stage-team\"]" "1d021c35-9e0c-42f7-8cb6-2d15d15811d0,stage-team"
terragrunt --working-dir=. import "digitalocean_database_user.users[\"db-postgresql-blr1-dev_temporal\"]" "1d021c35-9e0c-42f7-8cb6-2d15d15811d0,temporal"
terragrunt --working-dir=. import "digitalocean_database_user.users[\"db-postgresql-blr1-dev_unleash\"]" "1d021c35-9e0c-42f7-8cb6-2d15d15811d0,unleash"
terragrunt --working-dir=. import "digitalocean_database_db.databases[\"db-postgresql-blr1-dev_defaultdb\"]" "1d021c35-9e0c-42f7-8cb6-2d15d15811d0,defaultdb"
terragrunt --working-dir=. import "digitalocean_database_db.databases[\"db-postgresql-blr1-dev_temporal\"]" "1d021c35-9e0c-42f7-8cb6-2d15d15811d0,temporal"
terragrunt --working-dir=. import "digitalocean_database_db.databases[\"db-postgresql-blr1-dev_temporal_visibility\"]" "1d021c35-9e0c-42f7-8cb6-2d15d15811d0,temporal_visibility"
terragrunt --working-dir=. import "digitalocean_database_db.databases[\"db-postgresql-blr1-dev_unleash\"]" "1d021c35-9e0c-42f7-8cb6-2d15d15811d0,unleash"
terragrunt --working-dir=. import "digitalocean_database_db.databases[\"db-postgresql-blr1-dev_verge\"]" "1d021c35-9e0c-42f7-8cb6-2d15d15811d0,verge"
terragrunt --working-dir=. import "digitalocean_database_db.databases[\"db-postgresql-blr1-dev_verge-edge\"]" "1d021c35-9e0c-42f7-8cb6-2d15d15811d0,verge-edge"
terragrunt --working-dir=. import "digitalocean_database_db.databases[\"db-postgresql-blr1-dev_verge-stage\"]" "1d021c35-9e0c-42f7-8cb6-2d15d15811d0,verge-stage"
terragrunt --working-dir=. import "digitalocean_database_firewall.firewall_rules[\"db-postgresql-blr1-dev\"]" "1d021c35-9e0c-42f7-8cb6-2d15d15811d0"
terragrunt --working-dir=. import "digitalocean_database_cluster.clusters[\"log-forwarder-kafka-blr1\"]" "5d7ddd6c-3782-44bf-b838-d9f17e3b192b"
terragrunt --working-dir=. import "digitalocean_database_user.users[\"log-forwarder-kafka-blr1_admin\"]" "5d7ddd6c-3782-44bf-b838-d9f17e3b192b,admin"
terragrunt --working-dir=. import "digitalocean_database_user.users[\"log-forwarder-kafka-blr1_app\"]" "5d7ddd6c-3782-44bf-b838-d9f17e3b192b,app"
terragrunt --working-dir=. import "digitalocean_database_user.users[\"log-forwarder-kafka-blr1_app-consumer\"]" "5d7ddd6c-3782-44bf-b838-d9f17e3b192b,app-consumer"
terragrunt --working-dir=. import "digitalocean_database_user.users[\"log-forwarder-kafka-blr1_doadmin\"]" "5d7ddd6c-3782-44bf-b838-d9f17e3b192b,doadmin"
terragrunt --working-dir=. import "digitalocean_database_firewall.firewall_rules[\"log-forwarder-kafka-blr1\"]" "5d7ddd6c-3782-44bf-b838-d9f17e3b192b"
terragrunt --working-dir=. import "digitalocean_database_kafka_topic.kafka_topics[\"log-forwarder-kafka-blr1_ahc-event-log\"]" "5d7ddd6c-3782-44bf-b838-d9f17e3b192b,ahc-event-log"
terragrunt --working-dir=. import "digitalocean_database_kafka_topic.kafka_topics[\"log-forwarder-kafka-blr1_cdn-cp-traffic-logs-access-capnp\"]" "5d7ddd6c-3782-44bf-b838-d9f17e3b192b,cdn-cp-traffic-logs-access-capnp"
terragrunt --working-dir=. import "digitalocean_database_kafka_topic.kafka_topics[\"log-forwarder-kafka-blr1_cdn-logs-access\"]" "5d7ddd6c-3782-44bf-b838-d9f17e3b192b,cdn-logs-access"
terragrunt --working-dir=. import "digitalocean_database_kafka_topic.kafka_topics[\"log-forwarder-kafka-blr1_cdn-logs-access-capnp\"]" "5d7ddd6c-3782-44bf-b838-d9f17e3b192b,cdn-logs-access-capnp"
terragrunt --working-dir=. import "digitalocean_database_kafka_topic.kafka_topics[\"log-forwarder-kafka-blr1_cdn-logs-detmit\"]" "5d7ddd6c-3782-44bf-b838-d9f17e3b192b,cdn-logs-detmit"
terragrunt --working-dir=. import "digitalocean_database_kafka_topic.kafka_topics[\"log-forwarder-kafka-blr1_eventlog\"]" "5d7ddd6c-3782-44bf-b838-d9f17e3b192b,eventlog"
terragrunt --working-dir=. import "digitalocean_database_kafka_topic.kafka_topics[\"log-forwarder-kafka-blr1_purge\"]" "5d7ddd6c-3782-44bf-b838-d9f17e3b192b,purge"
terragrunt --working-dir=. import "digitalocean_database_kafka_topic.kafka_topics[\"log-forwarder-kafka-blr1_redins\"]" "5d7ddd6c-3782-44bf-b838-d9f17e3b192b,redins"
terragrunt --working-dir=. import "digitalocean_database_kafka_topic.kafka_topics[\"log-forwarder-kafka-blr1_sample\"]" "5d7ddd6c-3782-44bf-b838-d9f17e3b192b,sample"
terragrunt --working-dir=. import "digitalocean_database_kafka_topic.kafka_topics[\"log-forwarder-kafka-blr1_smartrouting-eventlog\"]" "5d7ddd6c-3782-44bf-b838-d9f17e3b192b,smartrouting-eventlog"
terragrunt --working-dir=. import "digitalocean_database_kafka_topic.kafka_topics[\"log-forwarder-kafka-blr1_tcp-proxy-access\"]" "5d7ddd6c-3782-44bf-b838-d9f17e3b192b,tcp-proxy-access"
terragrunt --working-dir=. import "digitalocean_database_kafka_topic.kafka_topics[\"log-forwarder-kafka-blr1_waflog\"]" "5d7ddd6c-3782-44bf-b838-d9f17e3b192b,waflog"
terragrunt --working-dir=. import "digitalocean_database_kafka_topic.kafka_topics[\"log-forwarder-kafka-blr1_workers-user\"]" "5d7ddd6c-3782-44bf-b838-d9f17e3b192b,workers-user"
terragrunt --working-dir=. import "digitalocean_database_cluster.clusters[\"prod-vergecloud-db-postgresql-blr1-37804\"]" "332ca9d7-fd04-42a6-ac4a-6fe61138a016"
terragrunt --working-dir=. import "digitalocean_database_user.users[\"prod-vergecloud-db-postgresql-blr1-37804_app\"]" "332ca9d7-fd04-42a6-ac4a-6fe61138a016,app"
terragrunt --working-dir=. import "digitalocean_database_user.users[\"prod-vergecloud-db-postgresql-blr1-37804_doadmin\"]" "332ca9d7-fd04-42a6-ac4a-6fe61138a016,doadmin"
terragrunt --working-dir=. import "digitalocean_database_user.users[\"prod-vergecloud-db-postgresql-blr1-37804_readonly\"]" "332ca9d7-fd04-42a6-ac4a-6fe61138a016,readonly"
terragrunt --working-dir=. import "digitalocean_database_db.databases[\"prod-vergecloud-db-postgresql-blr1-37804_defaultdb\"]" "332ca9d7-fd04-42a6-ac4a-6fe61138a016,defaultdb"
terragrunt --working-dir=. import "digitalocean_database_db.databases[\"prod-vergecloud-db-postgresql-blr1-37804_verge\"]" "332ca9d7-fd04-42a6-ac4a-6fe61138a016,verge"
terragrunt --working-dir=. import "digitalocean_database_firewall.firewall_rules[\"prod-vergecloud-db-postgresql-blr1-37804\"]" "332ca9d7-fd04-42a6-ac4a-6fe61138a016"
