# Include the root configuration
# include "root" {
#   path = find_in_parent_folders("root.hcl")
# }

# # Configure the Terraform module source
# terraform {
#   source = "../../modules/helm-chart"
# }

# # Explicitly set the Terraform binary name for OpenTofu compatibility
# terraform_binary = "tofu"

# # Generate provider configuration
# generate "providers" {
#   path      = "providers.tf"
#   if_exists = "overwrite_terragrunt"
#   contents  = <<EOF
#   terraform {
#     required_version = ">= 1.0"
#     required_providers {
#       helm = {
#         source  = "hashicorp/helm"
#         version = ">= 2.5.0"
#       }
#       kubernetes = {
#         source  = "hashicorp/kubernetes"
#         version = ">= 2.10.0"
#       }
#       vault = {
#         source  = "hashicorp/vault"
#         version = ">= 4.0"
#       }
#     }
#   }

#   provider "helm" {
#     kubernetes {
#       host                   = var.cluster_endpoint
#       cluster_ca_certificate = base64decode(var.cluster_ca_certificate)
#       token                  = var.client_token
#     }
#   }

#   provider "kubernetes" {
#     host                   = var.cluster_endpoint
#     cluster_ca_certificate = base64decode(var.cluster_ca_certificate)
#     token                  = var.client_token
#   }

#   provider "vault" {
#     # Vault provider configuration will be set by environment variables
#     # VAULT_ADDR, VAULT_TOKEN, etc.
#   }

#   variable "cluster_endpoint" {
#     description = "The DOKS cluster API server endpoint"
#     type        = string
#   }

#   variable "cluster_ca_certificate" {
#     description = "Base64 encoded CA certificate for the DOKS cluster"
#     type        = string
#     sensitive   = true
#   }

#   variable "client_token" {
#     description = "The client token for authenticating to the DOKS cluster"
#     type        = string
#     sensitive   = true
#   }
# EOF
# }

# # -----------------------------------------------------------------------------
# # Helper Commands (DO NOT UNCOMMENT - Reference Only)
# # -----------------------------------------------------------------------------
# # Export existing Helm values to files:
# # - helm get values vault -n vault > vault-existing-values.yaml
# # - helm get values temporal -n temporal > temporal-existing-values.yaml
# #
# # Reference these values in your charts:
# # - values_files = ["vault-existing-values.yaml"]

# # -----------------------------------------------------------------------------
# # Chart Constants and Defaults (must be at top-level for Terragrunt)
# # -----------------------------------------------------------------------------
# locals {
#   # Common settings
#   ENVIRONMENT = "prod" # Current deployment environment

#   # Vault settings
#   VAULT_CHART_VERSION   = "0.30.0" # Pinned for compatibility; update after testing
#   VAULT_TIMEOUT_SECONDS = 600      # 10 minutes for Vault install/upgrade
#   VAULT_NAMESPACE       = "vault"
#   VAULT_REPOSITORY_URL  = "https://helm.releases.hashicorp.com"

#   # Temporal settings (for future use)
#   # TEMPORAL_CHART_VERSION   = "0.58.0" # Pinned for stability; update after major releases
#   # TEMPORAL_TIMEOUT_SECONDS = 900      # 15 minutes for Temporal install/upgrade
#   # TEMPORAL_NAMESPACE       = "temporal"

#   # Environment Configuration (merged from second block)
#   env_vars = try(
#     yamldecode(file("${get_terragrunt_dir()}/${get_env("TG_ENV", "production")}.tfvars")),
#     {}
#   )
# }

# # Define inputs for the helm-chart module
# inputs = merge(local.env_vars, {
#   # Pass cluster connection details for provider configuration
#   cluster_endpoint       = dependency.kubernetes.outputs.cluster_endpoint
#   cluster_ca_certificate = dependency.kubernetes.outputs.cluster_ca_certificate
#   client_token           = dependency.kubernetes.outputs.client_token

#   # -----------------------------------------------------------------------------
#   # Error Handling
#   # -----------------------------------------------------------------------------
#   # These settings improve error handling and recovery
#   retry_on_provider_failure = true  # Retry operations on transient failures
#   error_output_truncation   = false # Show full error messages for debugging

#   # -----------------------------------------------------------------------------
#   # Chart Definitions
#   # -----------------------------------------------------------------------------
#   # Each chart is defined as an entry in the charts map
#   #
#   # IMPORTANT: When managing existing Helm releases with Terraform:
#   # - First apply will show changes as Terraform discovers real state
#   # - Use reuse_values=true to preserve existing chart configuration
#   # - Consider exporting existing values before management (see helper commands)
#   charts = {
#     vault = {
#       release_name     = "vault"
#       namespace        = local.VAULT_NAMESPACE
#       chart_name       = "vault"
#       chart_version    = local.VAULT_CHART_VERSION
#       repository_url   = local.VAULT_REPOSITORY_URL
#       create_namespace = false # Changed to match plan output
#       namespace_labels = {
#         "app.kubernetes.io/name"      = "vault"
#         "app.kubernetes.io/component" = "secrets-management"
#       }
#       set_values = [
#         {
#           name  = "server.dev.enabled"
#           value = "false"
#         },
#         {
#           name  = "ui.enabled"
#           value = "true"
#         },
#         {
#           name  = "injector.enabled"
#           value = "true"
#         }
#       ]
#       # Chart value configuration
#       values = {
#         # Define any inline YAML values here (optional)
#         # server:
#         #   dataStorage:
#         #     size: "10Gi"
#       }
#       # Note: values_files is deprecated in newer helm provider versions
#       # Use 'values' block above or 'set_values' for configuration
#       set_sensitive_values = []
#       reuse_values         = true # Preserve existing chart values during upgrades
#       wait                 = true
#       timeout              = local.VAULT_TIMEOUT_SECONDS
#       # Configure Kubernetes authentication for Vault
#       kubernetes_auth_enabled = true
#       kubernetes_auth_path    = "kubernetes-prod" # Path for Kubernetes auth mount
#       kubernetes_host         = dependency.kubernetes.outputs.cluster_endpoint
#       kubernetes_ca_cert      = dependency.kubernetes.outputs.cluster_ca_certificate

#       # SECURITY: Use one of the following methods to provide the token:
#       # 1. Environment variable (preferred for security)
#       token_reviewer_jwt = get_env("VAULT_SA_TOKEN", "")

#       # 2. Or retrieve programmatically using a data source (uncomment below)
#       # token_reviewer_jwt    = try(data.kubernetes_secret.vault_auth_token[0].data["token"], null)

#       # Kubernetes auth roles configuration
#       kubernetes_auth_roles = [
#         {
#           name_prefix                      = "myapp" # Prefix for the role name
#           bound_service_account_names      = ["vault-auth"]
#           bound_service_account_namespaces = ["vault"]
#           token_policies                   = ["default"] # Define appropriate policies
#           token_ttl                        = 3600        # 1 hour
#           token_max_ttl                    = 86400       # 24 hours
#         },
#         # Add additional roles as needed
#         # {
#         #   name_prefix                     = "api-service"
#         #   bound_service_account_names     = ["api-service"]
#         #   bound_service_account_namespaces = ["api"]
#         #   token_policies                  = ["api-read"]
#         #   token_ttl                       = 1800        # 30 minutes
#         #   token_max_ttl                   = 43200       # 12 hours
#         # }
#       ]
#     }
#   }
# })

# # Data source to fetch the vault-auth service account token
# # Uncomment and modify as needed
# # data "kubernetes_secret" "vault_auth_token" {
# #   count = get_env("VAULT_SA_TOKEN", "") == "" ? 1 : 0 # Only fetch if env var not set
# #
# #   provider = kubernetes
# #   metadata {
# #     name      = "vault-auth-token" # Name of the secret containing the SA token
# #     namespace = local.VAULT_NAMESPACE
# #   }
# #
# #   depends_on = [helm_release.chart["vault"]] # Ensure chart is deployed first
# # }

# # -----------------------------------------------------------------------------
# # Dependencies
# # -----------------------------------------------------------------------------
# dependency "kubernetes" {
#   config_path = "../kubernetes"
#   # Skip apply when kubernetes has changes in plan but we need to deploy vault first
#   skip_outputs = false

#   # Mock outputs for planning and init
#   mock_outputs = {
#     cluster_endpoint       = "https://mock-cluster-endpoint"
#     cluster_ca_certificate = "bW9jay1jYS1jZXJ0"
#     client_token           = "mock-token"
#   }
#   mock_outputs_allowed_terraform_commands = ["plan", "validate", "init"]
# }
