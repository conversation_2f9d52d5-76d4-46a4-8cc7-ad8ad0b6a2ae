# Include the root configuration
include "root" {
  path = find_in_parent_folders("root.hcl") # Assign the result to path and specify the filename
}

# Configure the Terraform module source
terraform {
  source = "../../modules/github_org" # Relative path to the module

  # If using private module registry:
  # source = "your-registry/modules/github_org"
}

# Define inputs for the github_org module
# These values define your desired state for the GitHub org
inputs = {
  # Read parent vars directly within inputs
  github_org_name = read_terragrunt_config(find_in_parent_folders("root.hcl")).locals.common_vars.github_org_name

  # --- Copy definitions from your refactored locals ---
  repositories = {
    "api-generator"  = { name = "api-generator", default_branch = "main", vulnerability_alerts = false }
    "api"            = { name = "api", default_branch = "dev", vulnerability_alerts = false }
    "web"            = { name = "web", default_branch = "dev", vulnerability_alerts = false }
    "infra"          = { name = "infra", default_branch = "main", vulnerability_alerts = false }
    "cdn-mfe"        = { name = "cdn-mfe", default_branch = "main", archived = true, has_wiki = false, vulnerability_alerts = false }
    "host-panel-mfe" = { name = "host-panel-mfe", default_branch = "main", archived = true, has_wiki = false, vulnerability_alerts = false }
    "release-test"   = { name = "release-test", default_branch = "develop", vulnerability_alerts = false }
    "cdn-core"       = { name = "cdn-core", default_branch = "dev", vulnerability_alerts = false }
    "platform"       = { name = "platform", default_branch = "main", vulnerability_alerts = false }
    "helm-charts" = {
      name                 = "helm-charts"
      default_branch       = "main"
      vulnerability_alerts = false
      pages = {
        build_type = "legacy"
        source = {
          branch = "gh-pages"
          path   = "/"
        }
      }
    }
  }

  members = {
    "banafsh13"       = "member"
    "danialzash"      = "member"
    "hoseinmohajer"   = "member"
    "iamsadjad"       = "admin"
    "m-e-h-r-d-a-a-d" = "admin"
    "mohamad-nikzad"  = "member"
    "mrbadri"         = "member"
    "mreza-sharifi"   = "admin"
    "saeid-a"         = "admin"
    "shaahinmo"       = "member"
    "ShokoufeFN"      = "member"
    "homayunj36"      = "member"
    "lebleuciel"      = "member"
    "dearrude"        = "member"
    "vergetools"      = "admin"
  }

  teams = {
    # Level 0
    "Owners"     = { name = "Owners", privacy = "closed", parent_slug = null }
    "SRE"        = { name = "SRE", privacy = "closed", parent_slug = null }
    "Reader"     = { name = "Reader", privacy = "closed", parent_slug = null }
    "Writer"     = { name = "Writer", privacy = "closed", parent_slug = null }
    "Maintainer" = { name = "Maintainer", privacy = "closed", parent_slug = null }
    # Level 1
    "BackEnd-R"  = { name = "BackEnd-R", privacy = "closed", parent_slug = "Reader" }
    "FrontEnd-R" = { name = "FrontEnd-R", privacy = "closed", parent_slug = "Reader" }
    # Level 2

    "BackEnd-W"  = { name = "BackEnd-W", privacy = "closed", parent_slug = "Writer" }
    "FrontEnd-W" = { name = "FrontEnd-W", privacy = "closed", parent_slug = "Writer" }
    # Level 3
    "BackEnd-M"  = { name = "BackEnd-M", privacy = "closed", parent_slug = "Maintainer" }
    "FrontEnd-M" = { name = "FrontEnd-M", privacy = "closed", parent_slug = "Maintainer" }
  }

  team_memberships = {
    "BackEnd-M" = {
      "shaahinmo" = "maintainer"
    }
    "BackEnd-R" = {
      "ShokoufeFN" = "member"
    }
    "BackEnd-W" = {
      "danialzash" = "member", "lebleuciel" = "member", "dearrude" = "member"
    }
    "FrontEnd-M" = {
      "shaahinmo" = "maintainer"
    }
    "FrontEnd-R" = {
      "ShokoufeFN" = "member", "lebleuciel" = "member", "danialzash" = "member", "dearrude" = "member"
    }
    "FrontEnd-W" = {
      "banafsh13"      = "member", "hoseinmohajer" = "member",
      "mohamad-nikzad" = "member", "mrbadri" = "member"
    }
    "Maintainer" = {
      "m-e-h-r-d-a-a-d" = "maintainer"
    }
    "Owners" = {
      "vergetools" = "maintainer", "m-e-h-r-d-a-a-d" = "maintainer"
    }
    "Reader" = {
      "vergetools" = "member"
    }
    "SRE" = {
      "iamsadjad" = "maintainer", "mreza-sharifi" = "maintainer", "saeid-a" = "maintainer", "homayunj36" = "maintainer"
    }
    "Writer" = {
      "vergetools" = "member"
    }
  }

  team_repository_permissions = {
    "BackEnd-M" = {
      "api" = "maintain", "cdn-core" = "maintain", "api-generator" = "maintain"
    }
    "BackEnd-R" = {
      "api" = "pull", "cdn-core" = "pull", "api-generator" = "pull"
    }
    "BackEnd-W" = {
      "api" = "push", "infra" = "push", "cdn-core" = "push", "helm-charts" = "push"
    }
    "FrontEnd-M" = {
      "web" = "maintain"
    }
    "FrontEnd-R" = {
      "web" = "pull"
    }
    "FrontEnd-W" = {
      "web" = "push"
    }
    "Reader" = {
      "api" = "pull", "web" = "pull", "cdn-core" = "pull", "api-generator" = "pull"
    }
    "SRE" = {
      "api" = "admin", "web" = "admin", "helm-charts" = "admin", "infra" = "admin", "cdn-core" = "admin", "api-generator" = "admin", "platform" = "admin"
    }
  }

  # ADD NEW branch_protections map:
  branch_protections = {
    # Protect 'main' branch of api-generator
    "api-gen-main" = {
      repository_key                  = "api-generator"
      pattern                         = "main"
      required_approving_review_count = 1
      require_conversation_resolution = true
      pull_request_bypassers          = ["vergecloud/sre"]
    }
    # Protect 'dev' branch of api
    "api-dev" = {
      repository_key                  = "api"
      pattern                         = "dev"
      required_approving_review_count = 2
      require_status_checks           = true
      status_check_contexts           = ["Validate Task ID in PR", "Lint PR Title"]
      strict_status_checks            = true
      pull_request_bypassers          = ["vergecloud/sre"]
    }
    # Protect 'staging' branch of api
    "api-staging" = {
      repository_key                  = "api"
      pattern                         = "staging"
      required_approving_review_count = 1
      pull_request_bypassers          = ["vergecloud/sre"]
      restrict_dismissals             = true
      dismissal_restrictions          = ["vergecloud/sre"]
    }
    # Protect 'main' branch of api
    "api-main" = {
      repository_key                  = "api"
      pattern                         = "main"
      required_approving_review_count = 1
      pull_request_bypassers          = ["vergecloud/sre"]
      restrict_dismissals             = true
      dismissal_restrictions          = ["vergecloud/sre"]
      restrict_pushes                 = true
      blocks_creations                = true
      push_allowances                 = ["vergecloud/sre"]
    }
    # Protect 'dev' branch of web
    "web-dev" = {
      repository_key                  = "web"
      pattern                         = "dev"
      required_approving_review_count = 2
      status_check_contexts           = ["Build and Verify", "Validate Task ID in PR", "e2e-tests", "Lint PR Title"]
      pull_request_bypassers          = ["vergecloud/sre"]
    }
    # Protect 'staging' branch of web
    "web-staging" = {
      repository_key                  = "web"
      pattern                         = "staging"
      required_approving_review_count = 1
      pull_request_bypassers          = ["vergecloud/sre"]
      restrict_dismissals             = true
      dismissal_restrictions          = ["vergecloud/sre"]
    }
    # Protect 'main' branch of web
    "web-main" = {
      repository_key                  = "web"
      pattern                         = "main"
      required_approving_review_count = 1
      pull_request_bypassers          = ["vergecloud/sre"]
      restrict_dismissals             = true
      dismissal_restrictions          = ["vergecloud/sre"]
      restrict_pushes                 = true
      blocks_creations                = true
      push_allowances                 = ["vergecloud/sre"]
    }
    # Protect 'main' branch of infra
    "infra-main" = {
      repository_key                  = "infra"
      pattern                         = "main"
      enforce_admins                  = true
      required_approving_review_count = 1
      pull_request_bypassers          = ["vergecloud/sre"]
    }
    # Protect 'main' branch of helm-charts
    "helm-charts-main" = {
      repository_key                  = "helm-charts"
      pattern                         = "main"
      required_approving_review_count = 1
      pull_request_bypassers          = ["vergecloud/sre"]
    }
    # # Protect 'dev' branch of cdn-core
    # "cdn-core-dev" = {
    #   repository_key                  = "cdn-core"
    #   pattern                         = "dev"
    #   required_approving_review_count = 2
    #   require_status_checks           = true
    #   status_check_contexts           = ["Validate Task ID in PR"]
    #   strict_status_checks            = true
    #   pull_request_bypassers          = ["vergecloud/sre"]
    # }
    # # Protect 'main' branch of cdn-core
    # "cdn-core-main" = {
    #   repository_key                  = "cdn-core"
    #   pattern                         = "main"
    #   required_approving_review_count = 2
    #   pull_request_bypassers          = ["vergecloud/sre"]
    # }
    # Protect 'main' branch of platform
    "platform-main" = {
      repository_key         = "platform"
      pattern                = "main"
      pull_request_bypassers = ["vergecloud/sre"]
    }
  }

  organization_settings = {
    email                         = "<EMAIL>"
    billing_email                 = "<EMAIL>"
    name                          = "Verge Cloud"
    company                       = "VergeCloud"
    blog                          = "https://vergecloud.com"
    twitter_username              = null
    location                      = null
    description                   = ""
    has_organization_projects     = true
    has_repository_projects       = true
    default_repository_permission = "none"

    # Restrict repository/page creation to admins for safety
    members_can_create_repositories          = false
    members_can_create_public_repositories   = false
    members_can_create_private_repositories  = false
    members_can_create_internal_repositories = false
    members_can_create_pages                 = false
    members_can_create_public_pages          = false
    members_can_create_private_pages         = false
    members_can_fork_private_repositories    = false

    # Security best practices: enable all security features for new repos
    web_commit_signoff_required                                  = false
    advanced_security_enabled_for_new_repositories               = false
    dependabot_alerts_enabled_for_new_repositories               = true
    dependabot_security_updates_enabled_for_new_repositories     = true
    dependency_graph_enabled_for_new_repositories                = true
    secret_scanning_enabled_for_new_repositories                 = false
    secret_scanning_push_protection_enabled_for_new_repositories = false
  }
}