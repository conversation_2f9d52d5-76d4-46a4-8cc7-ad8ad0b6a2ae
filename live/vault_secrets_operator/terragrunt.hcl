# Terragrunt configuration for Vault Secrets Operator
# This replaces direct Terraform Vault provider usage
skip = true
# terraform {
#   source = "../../modules/helm-chart"
# }

# # Include common configuration
# include "root" {
#   path = find_in_parent_folders("root.hcl")
# }

# # Dependencies
# dependencies {
#   paths = ["../helm"] # Ensure Vault is deployed first
# }

# # -----------------------------------------------------------------------------
# # ENVIRONMENT AND VAULT CONFIGURATION
# # -----------------------------------------------------------------------------
# locals {
#   # Environment detection - use parent config or derive from path
#   parent_config    = try(read_terragrunt_config(find_in_parent_folders("root.hcl")), {})
#   environment_path = basename(dirname(get_terragrunt_dir()))
#   environment = try(
#     local.parent_config.locals.environment,
#     contains(["dev", "development", "prod", "production", "stage", "staging"], local.environment_path) ? local.environment_path : "production"
#   )

#   # Vault service configuration constants
#   VAULT_NAMESPACE    = "vault"
#   VAULT_SERVICE_NAME = "vault.vault.svc.cluster.local"
#   VAULT_PORT         = 8200

#   # Environment-aware protocol selection
#   VAULT_PROTOCOL = contains(["prod", "production"], local.environment) ? "https" : "http"

#   # Construct vault address dynamically
#   vault_address = "${local.VAULT_PROTOCOL}://${local.VAULT_SERVICE_NAME}:${local.VAULT_PORT}"
# }

# # # Generate provider configuration
# # generate "providers" {
# #   path      = "providers.tf"
# #   if_exists = "overwrite_terragrunt"
# #   contents  = <<EOF
# # terraform {
# #   required_version = ">= 1.0"
# #   required_providers {
# #     helm = {
# #       source  = "hashicorp/helm"
# #       version = ">= 2.5.0"
# #     }
# #     kubernetes = {
# #       source  = "hashicorp/kubernetes"
# #       version = ">= 2.10.0"
# #     }
# #   }
# # }

# # provider "helm" {
# #   kubernetes {
# #     host                   = var.cluster_endpoint
# #     cluster_ca_certificate = base64decode(var.cluster_ca_certificate)
# #     token                  = var.client_token
# #   }
# # }

# # provider "kubernetes" {
# #   host                   = var.cluster_endpoint
# #   cluster_ca_certificate = base64decode(var.cluster_ca_certificate)
# #   token                  = var.client_token
# # }

# # variable "cluster_endpoint" {
# #   description = "The DOKS cluster API server endpoint"
# #   type        = string
# # }

# # variable "cluster_ca_certificate" {
# #   description = "Base64 encoded CA certificate for the DOKS cluster"
# #   type        = string
# #   sensitive   = true
# # }

# # variable "client_token" {
# #   description = "The client token for authenticating to the DOKS cluster"
# #   type        = string
# #   sensitive   = true
# # }
# # EOF
# # }

# # Inputs for the Helm Chart module
# inputs = merge({
#   # Environment configuration (dynamic)
#   environment = local.environment

#   # Vault connection configuration (dynamic)
#   vault_address = local.vault_address

#   # Vault integration for secure repository credentials
#   vault_enabled              = true
#   vault_address              = local.vault_address
#   vault_auth_method          = "kubernetes"
#   vault_kubernetes_role      = "helm-chart-role"
#   vault_kubernetes_auth_path = "kubernetes"
#   vault_skip_tls_verify      = local.environment != "prod"

#   # Configure vault credentials for HashiCorp repository (if needed)
#   vault_credentials_config = {
#     "vault-secrets-operator" = {
#       credentials_path = "helm/repositories/hashicorp"
#       username_key     = "username"
#       password_key     = "password"
#       enabled          = false # HashiCorp public repo doesn't need auth
#     }
#   }
#   }, {

#   # Pass cluster connection details for provider configuration
#   cluster_endpoint       = dependency.kubernetes.outputs.cluster_endpoint
#   cluster_ca_certificate = dependency.kubernetes.outputs.cluster_ca_certificate
#   client_token           = dependency.kubernetes.outputs.client_token

#   # Helm chart configuration
#   charts = {
#     vault-secrets-operator = {
#       release_name     = "vault-secrets-operator"
#       namespace        = "vault-secrets-operator-system"
#       chart_name       = "vault-secrets-operator"
#       chart_version    = "0.8.1"
#       repository_url   = "https://helm.releases.hashicorp.com"
#       create_namespace = true
#       namespace_labels = {
#         "app.kubernetes.io/name"      = "vault-secrets-operator"
#         "app.kubernetes.io/component" = "secrets-management"
#       }
#       set_values = [
#         {
#           name  = "defaultVaultConnection.enabled"
#           value = "true"
#         },
#         {
#           name  = "defaultVaultConnection.address"
#           value = local.vault_address
#         },
#         {
#           name  = "defaultVaultConnection.skipTLSVerify"
#           value = local.environment != "prod" ? "true" : "false"
#         }
#       ]
#       wait    = true
#       timeout = 600
#     }
#   }
# })

# # Dependencies
# dependency "kubernetes" {
#   config_path = "../kubernetes"
#   # Mock outputs for planning
#   mock_outputs = {
#     cluster_endpoint       = "https://mock-cluster-endpoint"
#     cluster_ca_certificate = "bW9jay1jYS1jZXJ0"
#     client_token           = "mock-token"
#   }
#   mock_outputs_allowed_terraform_commands = ["plan", "validate"]
# }
